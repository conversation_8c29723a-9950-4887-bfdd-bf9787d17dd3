<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 会员任务列管理
 *
 * @icon fa fa-circle-o
 */
class Usertask extends Backend
{

    /**
     * Usertask模型对象
     * @var \app\admin\model\Usertask
     */
    protected $model = null;

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = ['*'];

    /**
     * 是否开启数据限制
     * 禁用自动的admin_id过滤，因为user_task表中没有admin_id字段
     * @var bool
     */
    protected $dataLimit = false;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Usertask;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 临时注释权限控制，确保基本功能正常
            // try {
            //     $allowedUserIds = $this->getAllowedUserIds();
            //     if ($allowedUserIds !== null) {
            //         if (empty($allowedUserIds)) {
            //             // 如果没有允许的用户，返回空结果
            //             $result = array("total" => 0, "rows" => []);
            //             return json($result);
            //         }
            //         $where[] = ['user_id', 'in', $allowedUserIds];
            //     }
            // } catch (\Exception $e) {
            //     // 权限控制出错时，记录日志并返回空结果
            //     \think\Log::write('Usertask权限控制错误: ' . $e->getMessage(), 'error');
            //     $result = array("total" => 0, "rows" => []);
            //     return json($result);
            // }

            $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','user_id','type','task_name','task_image','group_task_name','group_task_image','task_sn','commission','ratio','pirce','multiple','createtime','user_status','user_status_time','task_set_number','money']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['username']);
				$row->createtime = date('Y-m-d H:i:s',$row->createtime);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取当前管理员允许查看的用户ID列表
     * @return array|null
     */
    protected function getAllowedUserIds()
    {
        // 临时简化：如果是超级管理员(admin)，不限制数据
        if ($this->auth->isSuperAdmin()) {
            return null;
        }

        // 临时简化：只查看当前管理员自己的用户，不包含下级
        $currentAdminId = $this->auth->id;

        // 根据管理员ID获取对应的用户ID列表
        $userIds = \think\Db::name('user')
            ->where('admin_id', $currentAdminId)
            ->column('id');

        return $userIds ?: []; // 如果没有用户，返回空数组
    }

    /**
     * 递归获取所有下级管理员ID
     * @param int $adminId 管理员ID
     * @return array
     */
    protected function getAllSubAdminIds($adminId)
    {
        $adminIds = [];

        // 查找直接下级
        $subAdmins = \think\Db::name('admin')
            ->where('pid', $adminId)
            ->where('status', 'normal')
            ->column('id');

        foreach ($subAdmins as $subAdminId) {
            $adminIds[] = $subAdminId;
            // 递归查找下级的下级
            $subSubAdminIds = $this->getAllSubAdminIds($subAdminId);
            $adminIds = array_merge($adminIds, $subSubAdminIds);
        }

        return array_unique($adminIds);
    }

}
