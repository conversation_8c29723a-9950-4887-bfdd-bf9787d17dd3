<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 会员任务列管理
 *
 * @icon fa fa-circle-o
 */
class Usertask extends Backend
{

    /**
     * Usertask模型对象
     * @var \app\admin\model\Usertask
     */
    protected $model = null;

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Usertask;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 构建查询对象
            $query = $this->model->with(['user']);

            // 添加数据权限过滤
            $adminIds = $this->getDataLimitAdminIds();
            if ($adminIds !== null) {
                // 使用JOIN查询来过滤数据
                $query = $query->alias('ut')
                    ->join('fa_user u', 'ut.user_id = u.id', 'LEFT')
                    ->where('u.admin_id', 'in', $adminIds);
            }

            $list = $query->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','user_id','type','task_name','task_image','group_task_name','group_task_image','task_sn','commission','ratio','pirce','multiple','createtime','user_status','user_status_time','task_set_number','money']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['username']);
				$row->createtime = date('Y-m-d H:i:s',$row->createtime);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取数据权限限制的管理员ID列表
     * @return array|null
     */
    protected function getDataLimitAdminIds()
    {
        // 如果是超级管理员(admin)，不限制数据
        if ($this->auth->isSuperAdmin()) {
            return null;
        }

        // 获取当前管理员ID
        $currentAdminId = $this->auth->id;

        // 获取当前管理员及其所有下级管理员的ID
        $adminIds = $this->getAllSubAdminIds($currentAdminId);

        // 添加当前管理员自己
        $adminIds[] = $currentAdminId;

        return array_unique($adminIds);
    }

    /**
     * 递归获取所有下级管理员ID
     * @param int $adminId 管理员ID
     * @return array
     */
    protected function getAllSubAdminIds($adminId)
    {
        $adminIds = [];

        // 查找直接下级
        $subAdmins = \think\Db::name('admin')
            ->where('pid', $adminId)
            ->where('status', 'normal')
            ->column('id');

        foreach ($subAdmins as $subAdminId) {
            $adminIds[] = $subAdminId;
            // 递归查找下级的下级
            $subSubAdminIds = $this->getAllSubAdminIds($subAdminId);
            $adminIds = array_merge($adminIds, $subSubAdminIds);
        }

        return array_unique($adminIds);
    }

}
