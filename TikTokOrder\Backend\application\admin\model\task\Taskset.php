<?php

namespace app\admin\model\task;

use think\Model;
use traits\model\SoftDelete;

class Taskset extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'task_set';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    

    







    public function task()
    {
        return $this->belongsTo('app\admin\model\Task', 'task_ids', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
