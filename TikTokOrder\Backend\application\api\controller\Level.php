<?php

namespace app\api\controller;

use app\api\library\buiapi\Api;

class Level extends Api{

    protected $model = null;
	
	protected $noNeedRight = '*';
	protected $noNeedLogin = ['index'];
	protected $_allow_func = ['index'];
	protected $_search_field = ['status'];
	
	use \app\api\library\buiapi\traits\Api;
	
    public function _initialize(){
        parent::_initialize();
        $this->model = new \app\api\model\Level;
	}
	
	    /**
     * 列表 
     */
    public function index(){
        $this->request->filter('trim,strip_tags,xss_clean');
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $mixWhere = $this->buildwheres(implode(',',$this->_search_field));
        $list = $this->model->where($where)->where($mixWhere)->order($sort, $order)->paginate($limit);
        foreach ($list as $row) {
            $row->visible(['level','title','image','earnings_ratio']);
            
        }
		$list = $this->__handle_index__($list);
        return $this->success('',$list);
    }
}