<style type="text/css">
    .sm-st {
        background: #fff;
        padding: 20px;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        margin-bottom: 20px;
    }

    .sm-st-icon {
        width: 60px;
        height: 60px;
        display: inline-block;
        line-height: 60px;
        text-align: center;
        font-size: 30px;
        background: #eee;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        float: left;
        margin-right: 10px;
        color: #fff;
    }

    .sm-st-info {
        padding-top: 2px;
    }

    .sm-st-info span {
        display: block;
        font-size: 24px;
        font-weight: 600;
    }

    .orange {
        background: #fa8564 !important;
    }

    .tar {
        background: #45cf95 !important;
    }

    .sm-st .green {
        background: #86ba41 !important;
    }

    .pink {
        background: #AC75F0 !important;
    }

    .yellow-b {
        background: #fdd752 !important;
    }

    .stat-elem {

        background-color: #fff;
        padding: 18px;
        border-radius: 40px;

    }

    .stat-info {
        text-align: center;
        background-color: #fff;
        border-radius: 5px;
        margin-top: -5px;
        padding: 8px;
        -webkit-box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        font-style: italic;
    }

    .stat-icon {
        text-align: center;
        margin-bottom: 5px;
    }

    .st-red {
        background-color: #F05050;
    }

    .st-green {
        background-color: #27C24C;
    }

    .st-violet {
        background-color: #7266ba;
    }

    .st-blue {
        background-color: #23b7e5;
    }

    .stats .stat-icon {
        color: #28bb9c;
        display: inline-block;
        font-size: 26px;
        text-align: center;
        vertical-align: middle;
        width: 50px;
        float: left;
    }

    .stat {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }

    .stat .value {
        font-size: 20px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
    }

    .stat .name {
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 5px 0;
    }

    .stat.lg .value {
        font-size: 26px;
        line-height: 28px;
    }

    .stat-col {
        margin:0 0 10px 0;
    }
    .stat.lg .name {
        font-size: 16px;
    }

    .stat-col .progress {
        height: 2px;
    }

    .stat-col .progress-bar {
        line-height: 2px;
        height: 2px;
    }

    .item {
        padding: 30px 0;
    }


    #statistics .panel {
        min-height: 150px;
    }

    #statistics .panel h5 {
        font-size: 14px;
    }
</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null, false)}
        <ul class="nav nav-tabs">
            <li class="active"><a href="#one" data-toggle="tab">{:__('Dashboard')}</a></li>
            <li><a href="#two" data-toggle="tab">{:__('Custom')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">

                <div class="row">
                    <div class="col-sm-3 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon st-red"><i class="fa fa-users"></i></span>
                            <div class="sm-st-info">
                                <span>{$totaluser}</span>
                                {:__('Total user')}
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon st-violet"><i class="fa fa-magic"></i></span>
                            <div class="sm-st-info">
                                <span>{$totaladdon}</span>
                                {:__('Total addon')}
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon st-blue"><i class="fa fa-leaf"></i></span>
                            <div class="sm-st-info">
                                <span>{$attachmentnums}</span>
                                {:__('Total attachment')}
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon st-green"><i class="fa fa-user"></i></span>
                            <div class="sm-st-info">
                                <span>{$totaladmin}</span>
                                {:__('Total admin')}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div id="echart" class="btn-refresh" style="height:300px;width:100%;"></div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card sameheight-item stats">
                            <div class="card-block">
                                <div class="row row-sm stats-container">
                                    <div class="col-xs-6 stat-col">
                                        <div class="stat-icon"><i class="fa fa-rocket"></i></div>
                                        <div class="stat">
                                            <div class="value"> {$todayusersignup}</div>
                                            <div class="name"> {:__('Today user signup')}</div>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 20%"></div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6 stat-col">
                                        <div class="stat-icon"><i class="fa fa-vcard"></i></div>
                                        <div class="stat">
                                            <div class="value"> {$todayuserlogin}</div>
                                            <div class="name"> {:__('Today user login')}</div>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 20%"></div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6  stat-col">
                                        <div class="stat-icon"><i class="fa fa-calendar"></i></div>
                                        <div class="stat">
                                            <div class="value"> {$threednu}</div>
                                            <div class="name"> {:__('Three dnu')}</div>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 20%"></div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6 stat-col">
                                        <div class="stat-icon"><i class="fa fa-calendar-plus-o"></i></div>
                                        <div class="stat">
                                            <div class="value"> {$sevendnu}</div>
                                            <div class="name"> {:__('Seven dnu')}</div>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 20%"></div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6  stat-col">
                                        <div class="stat-icon"><i class="fa fa-user-circle"></i></div>
                                        <div class="stat">
                                            <div class="value"> {$sevendau}</div>
                                            <div class="name"> {:__('Seven dau')}</div>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 20%"></div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6  stat-col">
                                        <div class="stat-icon"><i class="fa fa-user-circle-o"></i></div>
                                        <div class="stat">
                                            <div class="value"> {$thirtydau}</div>
                                            <div class="name"> {:__('Thirty dau')}</div>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 20%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" style="margin-top:15px;" id="statistics">

                    <div class="col-lg-12">
                    </div>
                    <div class="col-xs-6 col-md-3">
                        <div class="panel bg-blue-gradient no-border">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <span class="label label-primary pull-right">{:__('Real time')}</span>
                                    <h5>{:__('Working addon count')}</h5>
                                </div>
                                <div class="panel-content">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h1 class="no-margins">{$totalworkingaddon}</h1>
                                            <div class="font-bold"><i class="fa fa-magic"></i>
                                                <small>{:__('Working addon count tips')}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6 col-md-3">
                        <div class="panel bg-teal-gradient no-border">
                            <div class="panel-body">
                                <div class="ibox-title">
                                    <span class="label label-primary pull-right">{:__('Real time')}</span>
                                    <h5>{:__('Database count')}</h5>
                                </div>
                                <div class="ibox-content">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h1 class="no-margins">{$dbtablenums}</h1>
                                            <div class="font-bold"><i class="fa fa-database"></i>
                                                <small>{:__('Database table nums')}</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h1 class="no-margins">{$dbsize|format_bytes=###,'',0}</h1>
                                            <div class="font-bold"><i class="fa fa-filter"></i>
                                                <small>{:__('Database size')}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-6 col-md-3">
                        <div class="panel bg-purple-gradient no-border">
                            <div class="panel-body">
                                <div class="ibox-title">
                                    <span class="label label-primary pull-right">{:__('Real time')}</span>
                                    <h5>{:__('Attachment count')}</h5>
                                </div>
                                <div class="ibox-content">

                                    <div class="row">
                                        <div class="col-md-6">
                                            <h1 class="no-margins">{$attachmentnums}</h1>
                                            <div class="font-bold"><i class="fa fa-files-o"></i>
                                                <small>{:__('Attachment nums')}</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h1 class="no-margins">{$attachmentsize|format_bytes=###,'',0}</h1>
                                            <div class="font-bold"><i class="fa fa-filter"></i>
                                                <small>{:__('Attachment size')}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6 col-md-3">
                        <div class="panel bg-green-gradient no-border">
                            <div class="panel-body">
                                <div class="ibox-title">
                                    <span class="label label-primary pull-right">{:__('Real time')}</span>
                                    <h5>{:__('Picture count')}</h5>
                                </div>
                                <div class="ibox-content">

                                    <div class="row">
                                        <div class="col-md-6">
                                            <h1 class="no-margins">{$picturenums}</h1>
                                            <div class="font-bold"><i class="fa fa-picture-o"></i>
                                                <small>{:__('Picture nums')}</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h1 class="no-margins">{$picturesize|format_bytes=###,'',0}</h1>
                                            <div class="font-bold"><i class="fa fa-filter"></i>
                                                <small>{:__('Picture size')}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="two">
                <div class="row">
                    <div class="col-xs-12">
                        {:__('Custom zone')}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
