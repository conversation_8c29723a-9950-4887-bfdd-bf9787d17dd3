<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>数学公式</title>
    <link rel="stylesheet" href="./mathquill/mathquill.css"/>
    <link rel="stylesheet" href="./formula.css"/>
    <style>
        html, body, .main {
            margin: 0;
            padding: 0;
            /*font-size: 12px;*/
        }

        .container {
            width: 100%;
            margin: 0 auto;

            font-size: 13px;
        }


        ul.tabs {
            margin: 0px 0 5px 0;
            padding: 0px;
            list-style: none;
        }

        ul.tabs li {
            background: none;
            color: #222;
            display: inline-block;
            padding: 5px 10px;
            cursor: pointer;
            font-weight: 600;
        }

        ul.tabs li.current {
            color: #222;
            border: 1px solid #ccc;
            border-radius: 2px;
        }

        .tab-content {
            display: none;
            padding: 0px;
        }

        .tab-content.current {
            display: inherit;
        }

        .mathquill-rendered-math {
            border: 1px solid #ccc;
            padding: 3px;
            width: 215px;
            font-size: 16px;
        }

        h3 {
            font-size: 14px;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<div class="main">
    <div class="container">
        <div>
            <ul class="tabs">
                <li class="tab-link current" data-tab="common">常用公式</li>
                <li class="tab-link" data-tab="symbol">符号</li>
                <li class="tab-link" data-tab="letter">字母</li>
            </ul>

            <div id="common" class="tab-content current">
            </div>
            <div id="symbol" class="tab-content">
            </div>
            <div id="letter" class="tab-content">
            </div>
        </div>

        <div style="width:100%;display:block;clear:both;">
            <div id="editor" style="width:50%;display:inline-block;float:left;">
                <h3>编辑</h3>
                <div>
                    <span id="editable-math" class="mathquill-editable"></span>
                </div>
            </div>

            <div id="preview" style="width:50%;display:inline-block;float:right;">
                <h3>预览</h3>
                <div id="preview-body">
                    <a id="codecogslink" target="_blank"><img id="codecogsimg"></a>
                </div>
            </div>
            <div style="clear:both;width:100%;display:block;content:' '"></div>
        </div>
        <div id="text">
            <h3>公式</h3>
            <div>
                <textarea id="latex-source" style="width:98%;vertical-align:top;border:1px solid #ccc;padding:3px;"></textarea>
            </div>
        </div>

    </div><!-- container -->
</div>

<script src="./jquery.min.js"></script>
<script src="./mathquill/mathquill.js"></script>
<script>
    $(function () {

        var getQueryParam = function (name, url) {
            if (!url) {
                url = window.location.href;
            }
            name = name.replace(/[\[\]]/g, "\\$&");
            var regex = new RegExp("[?&/]" + name + "([=/]([^&#/?]*)|&|#|$)"),
                results = regex.exec(url);
            if (!results)
                return null;
            if (!results[2])
                return '';
            return decodeURIComponent(results[2].replace(/\+/g, " "));
        };

        var previewUrl = getQueryParam('previewUrl') || "https://math.now.sh?from={latex}";
        var formula = {
            'common': [
                "{/}frac{ }{ }", "^{ }/_{ }", "x^{ }", "x_{ }", "x^{ }_{ }", "{/}bar{ }", "{/}sqrt{ }", "{/}nthroot{ }{ }",
                "{/}sum^{ }_{n=}", "{/}sum", "{/}log_{ }", "{/}ln", "{/}int_{ }^{ }", "{/}oint_{ }^{ }"
            ],
            'symbol': [
                "+", "-", "{/}pm", "{/}times", "{/}ast", "{/}div", "/", "{/}bigtriangleup",
                "=", "{/}ne", "{/}approx", ">", "<", "{/}ge", "{/}le", "{/}infty",
                "{/}cap", "{/}cup", "{/}because", "{/}therefore", "{/}subset", "{/}supset", "{/}subseteq", "{/}supseteq",
                "{/}nsubseteq", "{/}nsupseteq", "{/}in", "{/}ni", "{/}notin", "{/}mapsto", "{/}leftarrow", "{/}rightarrow",
                "{/}Leftarrow", "{/}Rightarrow", "{/}leftrightarrow", "{/}Leftrightarrow"
            ],
            'letter': [
                "{/}alpha", "{/}beta", "{/}gamma", "{/}delta", "{/}varepsilon", "{/}varphi", "{/}lambda", "{/}mu",
                "{/}rho", "{/}sigma", "{/}omega", "{/}Gamma", "{/}Delta", "{/}Theta", "{/}Lambda", "{/}Xi",
                "{/}Pi", "{/}Sigma", "{/}Upsilon", "{/}Phi", "{/}Psi", "{/}Omega"
            ]
        };

        var xMax = 0, yMax = 0;
        $.each(formula, function (k, v) {
            var contentHtml = [];
            $.each(v, function (i, f) {
                contentHtml.push('<li class="formula-latex-item" data-latex="' + f + '" style="background-position:-' + (xMax * 30) + 'px -' + (yMax * 30) + 'px"></li>');
                if (++xMax >= 8) {
                    ++yMax;
                    xMax = 0;
                }
            });
            yMax++;
            xMax = 0;
            $("#" + k).append('<div class="tab-pane"><ul>' + contentHtml.join('') + '</ul>');
        });

        $('ul.tabs li').click(function () {
            var tab_id = $(this).attr('data-tab');

            $('ul.tabs li').removeClass('current');
            $('.tab-content').removeClass('current');

            $(this).addClass('current');
            $("#" + tab_id).addClass('current');
        });

        var latexMath = $('#editable-math'), latexSource = $('#latex-source'), htmlSource = $('#html-source'), codecogsimg = $('#codecogsimg'), codecogslink = $('#codecogslink');

        var buildImgLink = function () {
            var latex = latexSource.val();
            var url = previewUrl.replace(/\{latex\}/ig, latex);
            codecogslink.attr('href', url);
            codecogsimg.attr('src', url);
        };

        latexMath.on('keydown keypress', function () {
            setTimeout(function () {
                var latex = latexMath.mathquill('latex');
                latexSource.val(latex);
                buildImgLink();
            });
        });

        latexSource.on('keydown keypress', function () {
            var oldtext = latexSource.val();
            setTimeout(function () {
                var newtext = latexSource.val();
                if (newtext !== oldtext) {
                    latexMath.mathquill('latex', newtext);
                    buildImgLink();
                }
            });
        });

        $(document).on("click", ".formula-latex-item", function () {
            var latex = $(this).data("latex");
            latex = latex.replace("{/}", "\\");
            latexMath.mathquill('write', latex);
            latex = latexMath.mathquill('latex');
            latexSource.val(latex);
            buildImgLink();
        });

        var latex = getQueryParam("latex");
        if (latex) {
            latexSource.val(latex);
            latexMath.mathquill('latex', latexSource.val());
            buildImgLink();
        }

    });
</script>
</body>
</html>
