/**
 * <AUTHOR>
 * @since 18-9-11 下午9:02.
 */

// function _bindEvent(el, type, fn) {
// 	if (el.addEventListener){
// 		el.addEventListener(type, fn);
// 	} else if (el.attachEvent){
// 		el.attachEvent('on' + type, fn);
// 	}
// }
// _bindEvent(document.body, "DOMNodeInserted", function(e) {
// 	var className = e.target.className;
// 	if (className && className.indexOf("language-") != -1) {
// 		Prism.highlightElement(e.target);
// 	}
// })
