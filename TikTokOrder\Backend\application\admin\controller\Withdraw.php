<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;

/**
 * 消费
 *
 * @icon fa fa-circle-o
 */
class Withdraw extends Backend
{

    /**
     * Withdraw模型对象
     * @var \app\admin\model\Withdraw
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Withdraw;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $adminIDs = Db::name('admin')->column('id');
            $group_id = Db::name('auth_group_access')->where('uid',$this->auth->id)->value('group_id');
            if($group_id != 1) {
                $adminIDs = $this->auth->id;
            }
            if ($this->auth->id != 1) {
                $adminIDs = $this->auth->id;
            }
            $whereN['user.admin_id'] = ['in',$adminIDs];
            
            $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->where($whereN)
                    ->order($sort, $order)
                    ->paginate($limit)->each(function($item, $key){
                        $admin_id = Db::name('user')->where('id',$item['user_id'])->value('admin_id');
                        $adminNickname = Db::name('admin')->where('id',$admin_id)->value('nickname');
        				$item['adminnickname'] = $adminNickname?$adminNickname:'暂无';
        				return $item;
                    });

            foreach ($list as $row) {
                $row->visible(['id','user_id','order_sn','amount','fee','recieved_amount','address','address_type','status','createtime','updatetime']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['nickname']);
            }
           
            $result = array("total" => $list->total(), "rows" => $list->items());
            
            return json($result);
        }
        return $this->view->fetch();
    }
    
    /* 审核 */
    public function edit($ids = null){
        $row = $this->model->get($ids);
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a", [], 'strip_tags');
            if ($row['status'] != 0) {
                $this->error('当前提现申请已审核，无需重复审核');
            }
            $userInfo = Db::name('user')->where('id', $row['user_id'])->find();
            $agentInfo = Db::name('admin')->where('id',$userInfo['admin_id'])->find();
            if ($params['status'] == -1) {
                $log = [
                    'user_id' => $userInfo['id'],
                    'type' => 3,
                    'money' => $row['amount'],
                    'before' => $userInfo['money'],
                    'after' => bcadd($userInfo['money'],$row['amount'],2),
                    'memo' => '会员提现驳回',
                    'createtime' => time()
                ];
                Db::startTrans();
                try {
                    Db::name('user_money_log')->insert($log);
                    Db::name('withdraw')->where('id', $ids)->update(['status'=> '-1','updatetime'=>time(),'remark'=>$params['remark']]);
                    Db::name('user')->where('id',$userInfo['id'])->setInc('money',$row['amount']);;
                    // 提交事务
                    Db::commit();
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    $this->error('审核失败，请重试！');
                }
                /*$str = '驳回';
                $str = '代理：'.$agentInfo['nickname'].' 的客户：'. $userInfo['nickname'] . ' 消费'.$row['amount'].' 审核' . $str;
                if(strpos($userInfo['remark'], '代打') !== false) {
                    $str = '代理：'.$agentInfo['nickname'].' 的代打：'. $userInfo['nickname'] . ' 消费'.$row['amount'].' 审核' . $str;
                }
                sendTelegramMessage($str);*/
            } elseif ($params['status'] == 1) {
                // $str = '通过';
                Db::name('withdraw')->where('id',$row['id'])->update(['status'=>'1','updatetime'=>time()]);
                
                /*if(strpos($userInfo['remark'], '客户') !== false) {
                    $str = '代理：'.$agentInfo['nickname'].' 的客户：'. $userInfo['nickname'] . ' 消费'.$row['amount'].' 审核' . $str;
                    sendTelegramMessageBattlefieldReport($str);
                }
                $str = '代理：'.$agentInfo['nickname'].' 的客户：'. $userInfo['nickname'] . ' 消费'.$row['amount'].' 审核' . $str;
                if(strpos($userInfo['remark'], '代打') !== false) {
                    $str = '代理：'.$agentInfo['nickname'].' 的代打：'. $userInfo['nickname'] . ' 消费'.$row['amount'].' 审核' . $str;
                }
                sendTelegramMessage($str);*/
                
            } else {
                Db::name('withdraw')->where('id',$row['id'])->update(['address'=>$params['address']]);
            }
            
            $this->success();
        }
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->assign("row", $row);
        return $this->view->fetch();
    }
    
}
