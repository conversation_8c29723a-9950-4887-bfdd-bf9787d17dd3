<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
/**
 * 客服配置管理
 *
 * @icon fa fa-circle-o
 */
class Service extends Backend
{

    /**
     * Service模型对象
     * @var \app\admin\model\Service
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Service;
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $adminIDs = Db::name('admin')->column('id');
            
            $group_id = Db::name('auth_group_access')->where('uid',$this->auth->id)->value('group_id');
            
            if($group_id != 1) {
                $adminIDs = $this->auth->id;
            }
            
            $filter = json_decode($this->request->get('filter',''),1);
            
            if ($this->auth->id != 1) {
                $adminIDs = $this->auth->id;
            }
            $whereN['admin_id'] = ['in',$adminIDs];
            
            if (isset($filter['id']) && $filter['id'] > 0) {
                $whereN['id'] = ['=',$filter['id']];
            }
            
            if (isset($filter['title'])) {
                $whereN['title'] = ['like','%' . $filter['title'] . '%'];
            }
            
            if (isset($filter['status'])) {
                $whereN['status'] = ['=',$filter['status']];
            }
            
            $list = $this->model
                ->where($whereN)
                ->order($sort, $order)
                ->paginate($limit);
            $today = strtotime(date('Y-m-d',time()));
            foreach ($list as $k => $v) {
                $v->admin_nickname = Db::name('admin')->where('id',$v->admin_id)->value('nickname');
            }
            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a", [], 'strip_tags');
            if (empty($params['title'])) {
                $this->error('客服名称不可为空');
            }
            
            if (empty($params['image'])) {
                $this->error('主图不可为空');
            }
            if (empty($params['link'])) {
                $this->error('链接不可为空');
            }
            Db::name('service')->insert($params);
            $this->success();
        }
        
        $adminIDs = Db::name('admin')->column('id');
        if ($this->auth->id != 1) {
                $adminIDs = $this->auth->id;
        }
       
        
        $this->view->assign('adminList', build_select('row[admin_id]', (new \app\admin\model\Admin)->whereIn('id',$adminIDs)->column('id,nickname'), 1, ['class' => 'form-control selectpicker']));
        return $this->view->fetch();
    }
    
    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a", [], 'strip_tags');
            Db::name('service')->where('id', $row['id'])->update($params);
            $this->success();
        }
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIDs = Db::name('admin')->column('id');
        if ($this->auth->id != 1) {
                $adminIDs = $this->auth->id;
        }
        $where['admin_id'] = ['in',$adminIDs];
        $this->view->assign('adminList', build_select('row[admin_id]', (new \app\admin\model\Admin)->whereIn('id',$adminIDs)->column('id,nickname'), $row['admin_id'], ['class' => 'form-control selectpicker']));
        $this->view->assign('row', $row);
        return $this->view->fetch();
    }

}
