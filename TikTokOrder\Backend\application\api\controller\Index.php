<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\Log;
use think\Cache;
/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 首页
     *
     */
    public function index()
    {
        $this->success('请求成功');
    }
    
    /*获取首页轮播图*/
    public function getBanner() {
        $this->success('',config('site.slideshow'));
    }
    /*获取首页弹窗*/
    public function getPopup() {
        $this->success('',[config('site.windows')]);
    }
    /*关于我们*/
    public function getCase()
    {
        $this->success('',['info'=>config('site.case')]);
    }
    
    
    /*平台证书*/
    public function getCertificate() {
        $this->success('',[config('site.certificate')]);
    }
    
    /*平台条款*/
    public function getAgreement()
    {
        $this->success('', [config('site.platform_clause')]);
    }
    /*平台活动*/
    public function getActivity() {
        $this->success('',[config('site.activity')]);
    }
    /*常见问题*/
    public function getFAQ() {
        $this->success('',[config('site.common_problem')]);
    }
    
    
    /* 是否 服务客服 */
    public function checkServe() {
        if(config('site.open_all_day') != 1) {
            $currentTime  = date('H');
            $beginTime = substr(config('site.servicing_time_begin'),0,-6);
            $endTime = substr(config('site.servicing_time_end'),0,-6);
            if ($currentTime < $beginTime || $currentTime >= $endTime) {
                $this->error(__('Working hours are %d-%d, please talk during working hours',$beginTime,$endTime));
            } else {
                $this->success('OK');
            }
        } else {
             $this->success('OK');
        }
    }
    
    // 平台开放时间 
    public function openingHours() {
        
        if(config('site.open_all_day') != 1) {
            $currentTime  = date('H');
            $beginTime = substr(config('site.servicing_time_begin'),0,-6);
            $endTime = substr(config('site.servicing_time_end'),0,-6);
            if ($currentTime < $beginTime || $currentTime >= $endTime) {
                $this->error(__('Working hours are %d-%d',$beginTime,$endTime));
            } else {
                $this->success('OK');
            }
        } else {
            $this->success('OK');
        }
        
        
    }
    
   public function test() {
        /*$tong = sendTelegramMessage('这个是通知群测试消息'); // 通知群
        $zhan = sendTelegramMessageBattlefieldReport('这个是战报群测试消息'); // 战报群
        dump($tong);
        dump($zhan);*/
        //  Log::info('战报日志：'.var_export($ar,true));
        
      /*  $user['id'] = 11;
       if(!Cache::get('form_processing_'.$user['id'])) {
            Cache::set('form_processing_'.$user['id'],time(),10);
        } else {
            $this->error('Withdrawal is being processed, please do not submit again!');
        }
        Cache::rm('form_processing_'.$user['id']);*/
        // Session::delete('form_processing');
        
       /* $commission = bcdiv(config('site.commission'),100,2);
        echo $commission;*/
    }
}
