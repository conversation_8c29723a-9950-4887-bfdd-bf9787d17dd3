define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'template'], function ($, undefined, Backend, Table, Form, Template) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'user/user/index',
                    add_url: 'user/user/add',
                    edit_url: 'user/user/edit',
                    del_url: 'user/user/del',
                    multi_url: 'user/user/multi',
                    table: 'user',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'user.id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                        {
                            field: 'combined_agent_info',
                            title: __('关系网'),
                            operate: false, // No search/filter on this combined field by default
                            sortable: false, // Not sortable by default
                            formatter: function(value, row, index){
                                // Local HTML escaper function
                                function escapeHtml_local(unsafe) {
                                    if (typeof unsafe !== 'string') {
                                        // Attempt to convert to string if not already, useful for null/undefined or numbers
                                        unsafe = String(unsafe === null || typeof unsafe === 'undefined' ? '' : unsafe);
                                    }
                                    return $('<div/>').text(unsafe).html();
                                }
                                var adminNicknameText = row.admin_nickname ? '代理：' + escapeHtml_local(row.admin_nickname) : '代理：-';
                                var agentUsernameText = row.agentUsername ? '上级：' + escapeHtml_local(row.agentUsername) : '上级：-';
                                return adminNicknameText + '<br>' + agentUsernameText;
                            }
                        },
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE'},
                        {
                            field: 'loginip',
                            title: __('Loginip'),
                            operate: 'LIKE',
                            formatter: function(value, row, index){
                                if (!value) { return '-'; }
                                var encodedIp = encodeURIComponent(value);
                                // Local HTML escaper function (can be defined once if used in multiple formatters)
                                function escapeHtml_local(unsafe) {
                                    if (typeof unsafe !== 'string') {
                                        unsafe = String(unsafe === null || typeof unsafe === 'undefined' ? '' : unsafe);
                                    }
                                    return $('<div/>').text(unsafe).html();
                                }
                                var escapedIpText = escapeHtml_local(value);
                                return '<a href="https://www.ip138.com/iplookup.php?ip=' + encodedIp + '" target="_blank">' + escapedIpText + '</a>';
                            }
                        },
                        {field: 'money', title: __('Money'), operate: 'BETWEEN', sortable: true},
                        {field: 'commission', title: __('总佣金'), operate: false, sortable: true},
                        // {field: 'createtime', title: __('注册时间'), operate: 'BETWEEN',formatter: Table.api.formatter.datetime},
                        {field: 'createtime_', title: __('注册时间'),operate: false},
                        {field: 'task_number', title: __('Task_number'), operate: false, sortable: true},
                        {field: 'taskStatus', title: __('完成数/总数'), operate: false, sortable: true},
                        {field: 'task_complete_status', title: __('完成状态'), operate: false, sortable: true},
                        {field: 'status', title: __('Status'), formatter: Table.api.formatter.status, searchList: {normal: __('Normal'), hidden: __('Hidden')}},
                        {field: 'invitation_code', title: __('Invitation_code'), operate: 'LIKE'},
                        {field: 'remark', title: '备注', operate: 'LIKE'},
                        {
                            field: 'operate', title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'jointordersetup',
                                    text: __('联单设置'),
                                    title: __('联单设置'),
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    icon: 'fa fa-sitemap',
                                    url: 'user/user/jointOrderSetup',
                                },
                                {
                                    name: 'ajax',
                                    text: __('Resetquest relationshipmarriage'),
                                    classname: 'btn btn-xs btn-success btn-magic btn-ajax',
                                    icon: 'fa fa-magic',
                                    confirm: '是否确定重置任务?',
                                    url: 'user/user/reissueTask',
                                    success: function (data, ret) {
                                        Layer.alert(ret.msg);
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    }
                                },
                                {
                                    name: 'moneyrecharge',
                                    title: __('上分'),
                                    text: "上分",
                                    classname: 'btn btn-xs btn-success btn-magic btn-dialog',
                                    icon: 'fa fa-magic',
                                    url: 'user/user/moneyrecharge',
                                },
                                {
                                    name: 'edit',
                                    title: __('编辑'),
                                    text: "编辑",
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    icon: 'fa fa-pencil',
                                    url: 'user/user/edit',
                                },
                                {
                                    name: 'remark',
                                    title: __('备注'),
                                    text: "备注",
                                    classname: 'btn btn-xs btn-warning btn-dialog',
                                    icon: 'fa fa-comment',
                                    url: 'user/user/remark',
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        remark: function () {
            Controller.api.bindevent();
        },
        moneyrecharge: function () {
            // 获取当前弹窗的表单
            var form = $("form#edit-form");
            var userId = form.find("input[name='row[id]']").val();
            // 从新添加的隐藏字段读取昵称
            var currentNickname = form.find("input#h-nickname").val();

            form.on('submit', function(e) {
                e.preventDefault(); // 阻止表单的默认同步提交

                var moneyAmount = parseFloat(form.find("input[name='row[money]']").val());

                // 如果 currentNickname 为空（可能因为 $row.nickname 在HTML中也为空），提供一个默认值
                // 使用 jQuery进行HTML转义替换 Backend.htmlspecialchars
                var displayName = currentNickname ? $('<div>').text(currentNickname).html() : "该用户";

                if (isNaN(moneyAmount) || moneyAmount === 0) {
                    parent.Layer.alert('请输入有效的操作金额！', {icon: 7, title: '提示'});
                    return false;
                }

                var actionText = moneyAmount > 0 ? '增加' : '减少';
                var absMoneyAmount = Math.abs(moneyAmount);
                var confirmMsg = '您确定要为客户【' + displayName + '】(ID: ' + userId + ') ' + actionText + ' ' + absMoneyAmount + ' 金额吗？';

                parent.Layer.confirm(confirmMsg, {
                    title: '请确认操作',
                    btn: ['确定', '取消'],
                    icon: 3,
                    // ZIndex 很重要，确保确认框在最上层
                    zIndex: parent.Layer.zIndex ? parent.Layer.zIndex + 100 : undefined
                }, function (index) { // 用户点击了"确定"
                    parent.Layer.close(index); // 关闭确认框

                    // 执行实际的AJAX提交
                    Form.api.submit(form, function(data, ret){
                        // 成功回调
                        console.log("上分成功", data, ret);
                        parent.Toastr.success(ret.msg || '操作成功!');
                        // 获取弹窗的索引
                        var frameIndex = parent.layer.getFrameIndex(window.name);
                        if (frameIndex) {
                            parent.Layer.close(frameIndex); // 关闭"上分"弹窗
                        }
                        parent.$("#table").bootstrapTable('refresh'); // 刷新父页面的表格
                    }, function(data, ret){
                        // 失败回调
                        console.error("上分失败", data, ret);
                        parent.Toastr.error(ret.msg || '操作失败!');
                        // 这里通常不需要关闭确认框，因为它已经被关闭了
                        // 如果上分弹窗也需要关闭，可以在这里处理
                    });
                }, function() { // 用户点击了"取消" 或关闭按钮
                    // 不需要做特别处理
                });

                return false; // 再次阻止任何可能的后续默认提交行为
            });

            // 如果表单中没有提交按钮的特定ID，并且依赖 type="submit"
            // FastAdmin的Form.api.bindevent(form) 会自动处理点击 type="submit" 的按钮
            // 为确保我们的 submit 事件处理生效，上面的 form.on('submit', ...) 应该足够
            // 如果上面的方法在某些FastAdmin版本中不拦截，可以尝试给提交按钮一个ID，然后监听它的click事件
            // e.g., $('#btn-submit-money-recharge').on('click', function(e){ e.preventDefault(); /* ... confirm logic ... */ });
            // 但html中按钮是<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            // 它没有ID, 并且初始是disabled，FastAdmin会处理其状态。
            // 我们基于form的submit事件来拦截是比较通用的。

            // 确保表单验证等基础功能被绑定
            // 但我们不传递 success 回调，因为我们已经自己处理了提交逻辑
            Form.api.bindevent(form, function() { /*空回调，阻止默认提交后的行为*/ return false; }, null, function() { /* 表单验证前的回调，可以为空 */ return true; });

        },
        jointordersetup: function() {
            console.log("jointOrderSetup DIALOG: FUNCTION CALLED - TOP LEVEL");

            var form = $("form#joint-order-setup-dialog-form");
            var tbody = $("#joint-order-tbody-dialog", form); // 在表单上下文中查找，更精确
            var templateHtml = $("#joint-order-row-template-dialog").html();
            var hiddenDataInput = $("#h-joint_order_setup_data_dialog", form);

            console.log("jointOrderSetup DIALOG: Form found?", form.length);
            console.log("jointOrderSetup DIALOG: Tbody found?", tbody.length);
            console.log("jointOrderSetup DIALOG: Template HTML?", templateHtml ? templateHtml.substring(0, 50) + '...' : 'Not found or empty');
            console.log("jointOrderSetup DIALOG: Hidden input found?", hiddenDataInput.length);
            console.log("jointOrderSetup DIALOG: Initial hidden data value:", hiddenDataInput.val());

            function renderRow_dialog(itemData) {
                console.log("RenderRow_dialog: Called with item:", itemData);
                itemData = itemData || {};
                var rowHtml = '';
                if (!templateHtml) {
                    console.error("RenderRow_dialog: Template HTML missing!");
                    return;
                }

                try {
                    // 优先使用简单的字符串替换方法，确保兼容性
                    console.log("RenderRow_dialog: Using string replacement method for better compatibility.");
                    rowHtml = templateHtml
                        .replace(/<%=\s*item\.location\s*%>/g, itemData.location || '')
                        .replace(/<%=\s*item\.number\s*%>/g, itemData.number || '')
                        .replace(/<%=\s*item\.multiple\s*%>/g, itemData.multiple || '')
                        .replace(/<%=\s*item\.percentage\s*%>/g, itemData.percentage || '');

                    // 如果字符串替换失败，尝试其他模板引擎
                    if (rowHtml === templateHtml) {
                        if (Backend.api.tpl && typeof Backend.api.tpl === 'function') {
                            console.log("RenderRow_dialog: Fallback to Backend.api.tpl.");
                            rowHtml = Backend.api.tpl(templateHtml, {item: itemData});
                        } else if (Template && typeof Template === 'function') {
                            console.log("RenderRow_dialog: Fallback to Template.compile.");
                            if (!templateHtml || templateHtml.trim() === "") {
                                console.error("RenderRow_dialog: templateHtml empty, cannot compile.");
                                rowHtml = "";
                            } else {
                                var compiledTemplate = Template.compile(templateHtml);
                                rowHtml = compiledTemplate({item: itemData});
                            }
                        } else if (typeof _ === 'function' && typeof _.template === 'function') {
                            console.log("RenderRow_dialog: Fallback to _.template.");
                            var compiledTemplate = _.template(templateHtml);
                            rowHtml = compiledTemplate({item: itemData});
                        }
                    }
                } catch (e) {
                    console.error("RenderRow_dialog: Error rendering template:", e);
                    // 最后的备用方案：手动构建HTML
                    rowHtml = '<tr class="form-inline joint-order-data-row-dialog">' +
                        '<td><input type="text" name="location" class="form-control" value="' + (itemData.location || '') + '" placeholder="任务位置" size="5"/></td>' +
                        '<td><input type="text" name="number" class="form-control" value="' + (itemData.number || '') + '" placeholder="联单数" size="5" /></td>' +
                        '<td><input type="text" name="multiple" class="form-control" value="' + (itemData.multiple || '') + '" placeholder="佣金倍数" size="5" /></td>' +
                        '<td><input type="text" name="percentage" class="form-control" value="' + (itemData.percentage || '') + '" placeholder="百分比" size="5" /></td>' +
                        '<td><span class="btn btn-sm btn-danger btn-remove-joint-order-row-dialog"><i class="fa fa-times"></i></span></td>' +
                        '</tr>';
                }

                if (rowHtml) {
                    tbody.append(rowHtml);
                    bindRemoveEvents_dialog();
                } else {
                    console.error("RenderRow_dialog: Failed to generate row HTML");
                }
            }

            function updateHiddenData_dialog() {
                console.log("UpdateHiddenData_dialog: Called.");
                var data = [];
                var currentTbody = form.find("#joint-order-tbody-dialog");
                if (currentTbody.length === 0) {
                    console.error("UpdateHiddenData_dialog: TBODY NOT FOUND!");
                    hiddenDataInput.val("[]");
                    return;
                }

                currentTbody.find("tr.joint-order-data-row-dialog").each(function () {
                    var rowElement = $(this);

                    var locationInput = rowElement.find("input[name='location']")[0];
                    var location = locationInput ? locationInput.value.trim() : '';
                    var numberInput = rowElement.find("input[name='number']")[0];
                    var number = numberInput ? numberInput.value.trim() : '';
                    var multipleInput = rowElement.find("input[name='multiple']")[0];
                    var multiple = multipleInput ? multipleInput.value.trim() : '';
                    var percentageInput = rowElement.find("input[name='percentage']")[0];
                    var percentage = percentageInput ? percentageInput.value.trim() : '';

                    // 只有当至少有一个字段有值时才添加到数据中
                    if (location !== '' || number !== '' || multiple !== '' || percentage !== '') {
                        data.push({
                            location: location,
                            number: number,
                            multiple: multiple,
                            percentage: percentage
                        });
                        console.log("UpdateHiddenData_dialog: Added row data:", {location, number, multiple, percentage});
                    }
                });
                var jsonString = JSON.stringify(data);
                hiddenDataInput.val(jsonString);
                console.log("UpdateHiddenData_dialog: Final hidden data set to:", jsonString);
                console.log("UpdateHiddenData_dialog: Total rows collected:", data.length);
            }

            function bindRemoveEvents_dialog() {
                tbody.find(".btn-remove-joint-order-row-dialog").off('click.removeRowDialog').on('click.removeRowDialog', function () {
                    console.log("BindRemoveEvents_dialog: Remove button clicked.");
                    $(this).closest("tr.joint-order-data-row-dialog").remove();
                    updateHiddenData_dialog();
                });

                // 绑定输入框的change和blur事件，实时更新隐藏字段
                tbody.find("input[name='location'], input[name='number'], input[name='multiple'], input[name='percentage']")
                    .off('change.updateData blur.updateData')
                    .on('change.updateData blur.updateData', function() {
                        console.log("Input changed, updating hidden data...");
                        updateHiddenData_dialog();
                    });
            }

            var addButton = $("#btn-add-joint-order-row-dialog", form);
            addButton.off('click.addRowDialog').on('click.addRowDialog', function () {
                console.log("JointOrderSetup: Add button clicked.");
                renderRow_dialog({});
                updateHiddenData_dialog();
            });

            if (tbody.length && templateHtml && hiddenDataInput.length) {
                try {
                    var initialDataVal = hiddenDataInput.val();
                    var initialData = initialDataVal && initialDataVal.trim() !== "" ? JSON.parse(initialDataVal) : [];
                    if (Array.isArray(initialData)) {
                        initialData.forEach(function(item) {
                            renderRow_dialog(item);
                        });
                    }
                } catch (e) {
                    console.error("JointOrderSetup: Error parsing initial data:", e, "Raw data was:", initialDataVal);
                }
                bindRemoveEvents_dialog();
                setTimeout(function() {
                    console.log("JointOrderSetup: Delayed initial call to updateHiddenData_dialog.");
                    updateHiddenData_dialog();
                }, 100);
            } else {
                 console.error("JointOrderSetup: Critical elements not found. tbody:", tbody.length, "template:", !!templateHtml, "hiddenInput:", hiddenDataInput.length);
            }

            var beforeSubmitFinalAttempt = function() {
                console.log("BeforeSubmit: Called. Re-collecting all data.");
                var data = [];
                var currentTbody = form.find("#joint-order-tbody-dialog");
                if (currentTbody.length === 0) {
                    console.error("BeforeSubmit: TBODY NOT FOUND!");
                    hiddenDataInput.val("[]");
                    return true;
                }
                currentTbody.find("tr.joint-order-data-row-dialog").each(function () {
                    var rowElement = $(this);
                    var locationInput = rowElement.find("input[name='location']")[0];
                    var location = locationInput ? locationInput.value.trim() : '';
                    var numberInput = rowElement.find("input[name='number']")[0];
                    var number = numberInput ? numberInput.value.trim() : '';
                    var multipleInput = rowElement.find("input[name='multiple']")[0];
                    var multiple = multipleInput ? multipleInput.value.trim() : '';
                    var percentageInput = rowElement.find("input[name='percentage']")[0];
                    var percentage = percentageInput ? percentageInput.value.trim() : '';

                    // 只有当至少有一个字段有值时才添加到数据中
                    if (location !== '' || number !== '' || multiple !== '' || percentage !== '') {
                        data.push({
                            location: location,
                            number: number,
                            multiple: multiple,
                            percentage: percentage
                        });
                        console.log("BeforeSubmit: Added row data:", {location, number, multiple, percentage});
                    }
                });
                var jsonString = JSON.stringify(data);
                hiddenDataInput.val(jsonString);
                console.log("BeforeSubmit: Final hidden data set to:", jsonString);
                console.log("BeforeSubmit: Total rows collected:", data.length);
                return true;
            };

            Controller.api.bindevent(form,
                function(data, ret) {
                    parent.Toastr.success(ret.msg || '操作成功!');
                    var frameIndex = parent.layer.getFrameIndex(window.name);
                    if (frameIndex) {
                        parent.Layer.close(frameIndex);
                    }
                    parent.$("#table").bootstrapTable('refresh');
                },
                function(data, ret) {
                    parent.Toastr.error(ret.msg || '操作失败!');
                    console.error("JointOrderSetup: Form submission error. Response:", ret, "Data:", data);
                },
                beforeSubmitFinalAttempt
            );
        },
        api: {
            bindevent: function (form, success, error, beforeSubmitCallback) {
                var $form = form ? $(form) : $("form[role=form]");
                console.log("Controller.api.bindevent: Called. Preparing callbacks.");
                var callbacks = {};
                if (beforeSubmitCallback && typeof beforeSubmitCallback === 'function') {
                    callbacks.before = function(form_element) {
                        console.log("Controller.api.bindevent: Form.api.bindevent 'before' triggered.");
                        return beforeSubmitCallback(form_element);
                    };
                }
                try {
                    Form.api.bindevent($form, success, error, callbacks);
                } catch(e) {
                     console.error("Controller.api.bindevent: Error in setup:", e);
                     Form.api.bindevent($form, success, error);
                }
            }
        }
    };
    return Controller;
});