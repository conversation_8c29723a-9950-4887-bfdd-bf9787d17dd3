<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

/**
 * 会员提现
 */
class Withdraw extends Api
{
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [''];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
    public function withdraw(){
        $money = $this->request->post('money');
        $payPassword = $this->request->post('pay_password');
        $user = $this->auth;
        if ($user->pay_password != $this->auth->getEncryptPassword($payPassword, $user->pay_salt)) {
            $this->error(__('Pay password error'));
        }
        
        $withdraw_min = config('site.withdraw_min');
        if($money < $withdraw_min) {
            $this->error(__('The minimum withdrawal amount on the platform is %d',$withdraw_min));
        }
        
    }
    
}
