<?php

namespace app\admin\model;

use think\Model;


class Usertask extends Model
{

    

    

    // 表名
    protected $name = 'user_task';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'delecttime_text',
        'user_status_time_text'
    ];
    

    



    public function getDelecttimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['delecttime']) ? $data['delecttime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getUserStatusTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['user_status_time']) ? $data['user_status_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setDelecttimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setUserStatusTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
