<style>
    .profile-avatar-container {
        position: relative;
        width: 100px;
        margin: 0 auto;
    }

    .profile-avatar-container .profile-user-img {
        width: 100px;
        height: 100px;
    }

    .profile-avatar-container .profile-avatar-text {
        display: none;
    }

    .profile-avatar-container:hover .profile-avatar-text {
        display: block;
        position: absolute;
        height: 100px;
        width: 100px;
        background: #444;
        opacity: .6;
        color: #fff;
        top: 0;
        left: 0;
        line-height: 100px;
        text-align: center;
    }

    .profile-avatar-container button {
        position: absolute;
        top: 0;
        left: 0;
        width: 100px;
        height: 100px;
        opacity: 0;
    }
</style>
<div class="row animated fadeInRight">
    <div class="col-md-4">
        <div class="box box-primary">
            <div class="panel-heading">
                {:__('Profile')}
            </div>
            <div class="panel-body">

                <form id="update-form" role="form" data-toggle="validator" method="POST" action="{:url('general.profile/update')}">
                    {:token()}
                    <input type="hidden" id="c-avatar" name="row[avatar]" value="{$admin.avatar|htmlentities}"/>
                    <div class="box-body box-profile">

                        <div class="profile-avatar-container">
                            <img class="profile-user-img img-responsive img-circle" src="{$admin.avatar|cdnurl|htmlentities}" alt="">
                            <div class="profile-avatar-text img-circle">{:__('Click to edit')}</div>
                            <button type="button" id="faupload-avatar" class="faupload" data-input-id="c-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button>
                        </div>

                        <h3 class="profile-username text-center">{$admin.nickname|htmlentities}</h3>

                        <div class="form-group">
                            <label for="username" class="control-label">{:__('Username')}:</label>
                            <input type="text" class="form-control" id="username" name="row[username]" value="{$admin.username|htmlentities}" disabled/>
                        </div>
                        <div class="form-group">
                            <label for="mobile" class="control-label">{:__('Mobile')}:</label>
                            <input type="text" class="form-control" id="mobile" name="row[mobile]" value="{$admin.mobile|htmlentities}" disabled/>
                        </div>
                        <div class="form-group">
                            <label for="email" class="control-label">{:__('Email')}:</label>
                            <input type="text" class="form-control" id="email" name="row[email]" value="{$admin.email|htmlentities}" data-rule="required;email"/>
                        </div>
                        <div class="form-group">
                            <label for="nickname" class="control-label">{:__('Nickname')}:</label>
                            <input type="text" class="form-control" id="nickname" name="row[nickname]" value="{$admin.nickname|htmlentities}" data-rule="required"/>
                        </div>
                        <div class="form-group">
                            <label for="password" class="control-label">{:__('Password')}:</label>
                            <input type="password" class="form-control" id="password" placeholder="{:__('Leave password blank if dont want to change')}" autocomplete="new-password" name="row[password]" value="" data-rule="password"/>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">{:__('Submit')}</button>
                            <button type="reset" class="btn btn-default">{:__('Reset')}</button>
                        </div>

                    </div>
                </form>
            </div>
        </div>

    </div>
    <div class="col-md-8">
        <div class="panel panel-default panel-intro panel-nav">
            <div class="panel-heading">
                <ul class="nav nav-tabs">
                    <li class="active"><a href="#one" data-toggle="tab"><i class="fa fa-list"></i> {:__('Admin log')}</a></li>
                </ul>
            </div>
            <div class="panel-body">
                <div id="myTabContent" class="tab-content">
                    <div class="tab-pane fade active in" id="one">
                        <div class="widget-body no-padding">
                            <div id="toolbar" class="toolbar">
                                {:build_toolbar('refresh')}
                            </div>
                            <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%">

                            </table>

                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
</div>
