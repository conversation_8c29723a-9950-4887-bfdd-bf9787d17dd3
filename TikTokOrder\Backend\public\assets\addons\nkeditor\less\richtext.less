.richtext {
  margin: 8px 0 0;
  overflow: hidden;
  h3 {border-left:2px solid @brand-pink;display:block;font-size:18px;text-indent:14px;line-height:18px;font-weight:400;}
  p,ul,ol {
    font-family: 'Microsoft YaHei',san-serif;
    font-size: 14px !important;
    color: #555;
    line-height: 29px !important;
    white-space: pre-line;
    word-break: break-all
  }

  p {
    margin-top: 15px;
    margin-bottom: 15px;
    text-indent: 2em;
    text-align: justify;
    span {
      font-family: 'Microsoft YaHei',san-serif;
      font-size: 14px !important;
      color: #555;
      line-height: 29px !important;
      white-space: pre-line;
      word-break: break-all
    }
  }

  ul,ol {
    padding-left: 20px;
    li{
      text-indent: 0
    }
    p {
      font-size: 18px;
      text-indent: .5em
    }
  }

  ul li {
    list-style: outside square
  }

  ol li {
    list-style: outside decimal
  }

  strong {
    font-weight: 700;
    color: #333
  }

  img {
    max-width: 790px;
  }
  .pd-card {
    position: relative;
    height: 140px;
    width: 630px;
    margin: 23px auto;
    border: 1px solid #eee;
    .transition(box-shadow 300ms ease-out);
    &:hover {
      .box-shadow(0 2px 2px #eee);
    }
    .img-wrap {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      width: 140px;
      height: 140px;
      border-right: 1px solid #eee;
      img {
        width: 120px;
        height: 120px;
        margin: 10px;
      }
    }
    .card-info {
      .box-sizing(border-box);
      position: absolute;
      top: 17px;
      left: 165px;
      width: 436px;
    }
    .title {
      max-height: 56px;
      margin: 0;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-size: 16px;
      font-weight: 400;
      line-height: 28px;
      color: #202e3b;
      a {
        color: inherit;
        text-decoration: none;
      }
    }
    .card-b {
      position: absolute;
      top: 64px;
      left: 0;
      width: 100%;
    }
    .card-price, .merchant {
      float: left;
    }
    .card-price {
      margin-right: 22px;
      font-size: 24px;
      line-height: 34px;
      color: #fe5579;
    }
    .merchant {
      margin-top: 10px;
      color: #6e86ad;
    }
  }
}
