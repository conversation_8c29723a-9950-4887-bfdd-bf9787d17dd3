<?php

namespace app\api\controller;

use app\api\library\buiapi\Api;

class Task extends Api{

    protected $model = null;
	
	protected $noNeedRight = '*';
	protected $noNeedLogin = [];
	protected $_allow_func = ['view'];
	
	
	use \app\api\library\buiapi\traits\Api;
	
    public function _initialize(){
        parent::_initialize();
        $this->model = new \app\api\model\Task;
	}
	
    /*会员当前任务*/
    public function getUserTask(){
        $uid = $this->auth->id;
        $data = $this->model->where('uid',$uid)->find();
        
        return $this->success('ok', $data);
    }
	
}