    /**
     * 列表
     */
    public function index(){
        $this->request->filter('trim,strip_tags,xss_clean');
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $mixWhere = $this->buildwheres(implode(',',$this->_search_field));
        $list = $this->model{%relation_with_list%}->where($where)->where($mixWhere)->order($sort, $order)->paginate($limit);
        foreach ($list as $row) {
            {%visible_field_list%}
            {%relation_visible_field_list%}
        }
		$list = $this->__handle_index__($list);
        return $this->success('数据列表',$list);
    }