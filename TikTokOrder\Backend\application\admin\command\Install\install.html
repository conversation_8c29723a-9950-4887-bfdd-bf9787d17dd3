<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{:__('Installing FastAdmin')}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1">
    <meta name="renderer" content="webkit">

    <style>
        body {
            background: #f1f6fd;
            margin: 0;
            padding: 0;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body, input, button {
            font-family: 'Source Sans Pro', 'Helvetica Neue', Helvetica, 'Microsoft Yahei', Arial, sans-serif;
            font-size: 14px;
            color: #7E96B3;
        }

        .container {
            max-width: 480px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }

        a {
            color: #4e73df;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        h1 {
            margin-top: 0;
            margin-bottom: 10px;
        }

        h2 {
            font-size: 28px;
            font-weight: normal;
            color: #3C5675;
            margin-bottom: 0;
            margin-top: 0;
        }

        form {
            margin-top: 40px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group .form-field:first-child input {
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        .form-group .form-field:last-child input {
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        .form-field input {
            background: #fff;
            margin: 0 0 2px;
            border: 2px solid transparent;
            transition: background 0.2s, border-color 0.2s, color 0.2s;
            width: 100%;
            padding: 15px 15px 15px 180px;
            box-sizing: border-box;
        }

        .form-field input:focus {
            border-color: #4e73df;
            background: #fff;
            color: #444;
            outline: none;
        }

        .form-field label {
            float: left;
            width: 160px;
            text-align: right;
            margin-right: -160px;
            position: relative;
            margin-top: 15px;
            font-size: 14px;
            pointer-events: none;
            opacity: 0.7;
        }

        button, .btn {
            background: #3C5675;
            color: #fff;
            border: 0;
            font-weight: bold;
            border-radius: 4px;
            cursor: pointer;
            padding: 15px 30px;
            -webkit-appearance: none;
        }

        button[disabled] {
            opacity: 0.5;
        }

        .form-buttons {
            height: 52px;
            line-height: 52px;
        }

        .form-buttons .btn {
            margin-right: 5px;
        }

        #error, .error, #success, .success, #warmtips, .warmtips {
            background: #D83E3E;
            color: #fff;
            padding: 15px 20px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        #success {
            background: #3C5675;
        }

        #error a, .error a {
            color: white;
            text-decoration: underline;
        }

        #warmtips {
            background: #ffcdcd;
            font-size: 14px;
            color: #e74c3c;
        }

        #warmtips a {
            background: #ffffff7a;
            display: block;
            height: 30px;
            line-height: 30px;
            margin-top: 10px;
            color: #e21a1a;
            border-radius: 3px;
        }
    </style>
</head>

<body>
<div class="container">
    <h1>
        <svg width="80px" height="96px" viewBox="0 0 768 830" version="1.1" xmlns="http://www.w3.org/2000/svg"
             xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="logo" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                <path d="M64.433651,605.899968 C20.067302,536.265612 0,469.698785 0,389.731348 C0,174.488668 171.922656,0 384,0 C596.077344,0 768,174.488668 768,389.731348 C768,469.698785 747.932698,536.265612 703.566349,605.899968 C614.4,753.480595 441.6,870.4 384,870.4 C326.4,870.4 153.6,753.480595 64.433651,605.899968 L64.433651,605.899968 Z"
                      id="body" fill="#4e73df"></path>
                <path d="M429.648991,190.816 L430.160991,190.816 L429.648991,190.816 L429.648991,190.816 Z M429.648991,156 L427.088991,156 C419.408991,157.024 411.728991,160.608 404.560991,168.8 L403.024991,170.848 L206.928991,429.92 C198.736991,441.184 197.712991,453.984 204.368991,466.784 C210.512991,478.048 222.288991,485.728 235.600991,485.728 L336.464991,486.24 L304.208991,673.632 C301.648991,689.504 310.352991,705.376 325.200991,712.032 C329.808991,714.08 334.416991,714.592 339.536991,714.592 C349.776991,714.592 358.992991,709.472 366.160991,700.256 L561.744991,419.168 C569.936991,407.904 570.960991,395.104 564.304991,382.304 C557.648991,369.504 547.408991,363.36 533.072991,363.36 L432.208991,363.36 L463.952991,199.008 C464.464991,196.448 464.976991,193.376 464.976991,190.816 C464.976991,171.872 449.104991,156 431.184991,156 L429.648991,156 L429.648991,156 Z"
                      id="flash" fill="#FFFFFF"></path>
            </g>
        </svg>
    </h1>
    <h2>{:__('Installing FastAdmin')}</h2>
    <div>

        <form method="post">
            {if $errInfo}
            <div class="error">
                {$errInfo}
            </div>
            {/if}
            <div id="error" style="display:none"></div>
            <div id="success" style="display:none"></div>
            <div id="warmtips" style="display:none"></div>

            <div class="form-group">
                <div class="form-field">
                    <label>{:__('Mysql Hostname')}</label>
                    <input type="text" name="mysqlHostname" value="127.0.0.1" required="">
                </div>

                <div class="form-field">
                    <label>{:__('Mysql Database')}</label>
                    <input type="text" name="mysqlDatabase" value="" required="">
                </div>

                <div class="form-field">
                    <label>{:__('Mysql Username')}</label>
                    <input type="text" name="mysqlUsername" value="root" required="">
                </div>

                <div class="form-field">
                    <label>{:__('Mysql Password')}</label>
                    <input type="password" name="mysqlPassword">
                </div>

                <div class="form-field">
                    <label>{:__('Mysql Prefix')}</label>
                    <input type="text" name="mysqlPrefix" value="fa_">
                </div>

                <div class="form-field">
                    <label>{:__('Mysql Hostport')}</label>
                    <input type="number" name="mysqlHostport" value="3306">
                </div>
            </div>

            <div class="form-group">
                <div class="form-field">
                    <label>{:__('Admin Username')}</label>
                    <input name="adminUsername" value="admin" required=""/>
                </div>

                <div class="form-field">
                    <label>{:__('Admin Email')}</label>
                    <input name="adminEmail" value="<EMAIL>" required="">
                </div>

                <div class="form-field">
                    <label>{:__('Admin Password')}</label>
                    <input type="password" name="adminPassword" required="">
                </div>

                <div class="form-field">
                    <label>{:__('Repeat Password')}</label>
                    <input type="password" name="adminPasswordConfirmation" required="">
                </div>
            </div>

            <div class="form-group">
                <div class="form-field">
                    <label>{:__('Website')}</label>
                    <input type="text" name="siteName" value="{:__('My Website')}" required=""/>
                </div>

            </div>

            <div class="form-buttons">
                <!--@formatter:off-->
                <button type="submit" {:$errInfo?'disabled':''}>{:__('Install now')}</button>
                <!--@formatter:on-->
            </div>
        </form>

        <!-- jQuery -->
        <script src="__ROOT__/assets/libs/jquery/dist/jquery.min.js"></script>

        <script>
            $(function () {
                $('form :input:first').select();

                $('form').on('submit', function (e) {
                    e.preventDefault();
                    var form = this;
                    var $error = $("#error");
                    var $success = $("#success");
                    var $button = $(this).find('button')
                        .text("{:__('Installing')}")
                        .prop('disabled', true);
                    $.ajax({
                        url: "",
                        type: "POST",
                        dataType: "json",
                        data: $(this).serialize(),
                        success: function (ret) {
                            if (ret.code == 1) {
                                var data = ret.data;
                                $error.hide();
                                $(".form-group", form).remove();
                                $button.remove();
                                $("#success").text(ret.msg).show();

                                $buttons = $(".form-buttons", form);
                                $("<a class='btn' href='./'>{:__('Home')}</a>").appendTo($buttons);

                                if (typeof data.adminName !== 'undefined') {
                                    var url = location.href.replace(/install\.php/, data.adminName);
                                    $("#warmtips").html("{:__('Security tips')}" + '<a href="' + url + '">' + url + '</a>').show();
                                    $('<a class="btn" href="' + url + '" id="btn-admin" style="background:#4e73df">' + "{:__('Dashboard')}" + '</a>').appendTo($buttons);
                                }
                                localStorage.setItem("fastep", "installed");
                            } else {
                                $error.show().text(ret.msg);
                                $button.prop('disabled', false).text("{:__('Install now')}");
                                $("html,body").animate({
                                    scrollTop: 0
                                }, 500);
                            }
                        },
                        error: function (xhr) {
                            $error.show().text(xhr.responseText);
                            $button.prop('disabled', false).text("{:__('Install now')}");
                            $("html,body").animate({
                                scrollTop: 0
                            }, 500);
                        }
                    });
                    return false;
                });
            });
        </script>
    </div>
</div>
</body>
</html>
