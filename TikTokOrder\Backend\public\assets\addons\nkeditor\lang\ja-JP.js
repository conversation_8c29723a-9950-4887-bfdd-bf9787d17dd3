/*******************************************************************************
* <AUTHOR>　お名前
*******************************************************************************/

KindEditor.lang({
	source : 'HTMLコード',
	preview : 'プレビュー',
	undo : '戻る(Ctrl+Z)',
	redo : '進む(Ctrl+Y)',
	cut : 'カット(Ctrl+X)',
	copy : 'コピー(Ctrl+C)',
	paste : '貼り付け(Ctrl+V)',
	plainpaste : 'フォーマットされていないテキストとして貼り付ける',
	wordpaste : '「word」ドからペースト',
	selectall : 'すべて選択(Ctrl+A)',
	justifyleft : '左揃え',
	justifycenter : '中央揃え',
	justifyright : '右揃え',
	justifyfull : '両端揃え',
	insertorderedlist : '番号',
	insertunorderedlist : '箇条書き',
	indent : 'インデントを増やす',
	outdent : 'インデントを減らす',
	subscript : '下付き',
	superscript : '上付き',
	formatblock : '段落',
	fontname : 'フォント',
	fontsize : 'フォントサイズ',
	forecolor : 'フォントカラー',
	hilitecolor : 'テキストの背景',
	bold : '太字(Ctrl+B)',
	italic : '斜体(Ctrl+I)',
	underline : '下線(Ctrl+U)',
	strikethrough : '取り消し線',
	removeformat : 'フォーマットを削除',
	image : '画像',
	multiimage : '一括画像アップロード',
	flash : 'Flash',
	media : 'ビデオとオーディオ',
	table : 'テーブル',
	tablecell : 'セル',
	hr : '水平線を挿入する',
	emoticons : '絵文字を挿入する',
	link : 'ハイパーリンク',
	unlink : 'ハイパーリンクをキャンセル',
	fullscreen : 'フルスクリーン表示',
	about : 'について',
	print : 'プリント(Ctrl+P)',
	filemanager : 'ファイルスペース',
	code : 'プログラムコードを挿入',
	map : 'Googleマップ',
	baidumap : 'Baiduマップ',
	lineheight : '行間隔',
	clearhtml : 'HTMLコードをクリア',
	pagebreak : 'ページ区切りの挿入',
	quickformat : 'ワンクリックレイアウト',
	insertfile : 'ファイルの挿入',
	template : 'テンプレートの挿入',
	anchor : 'アンカー',
	yes : 'はい',
	no : 'いいえ',
	close : '閉じる',
	editImage : 'イメージプロパティ',
	deleteImage : 'イメージを削除',
	editFlash : 'Flashプロパティ',
	deleteFlash : 'Flashを削除',
	editMedia : 'ビデオとオーディオのプロパティ',
	deleteMedia : 'ビデオとオーディオを削除',
	editLink : 'ハイパーリンク属性',
	deleteLink : 'ハイパーリンクを削除',
	editAnchor : 'アンカー属性',
	deleteAnchor : 'アンカーを削除',
	tableprop : 'テーブル属性',
	tablecellprop : 'セル属性',
	tableinsert : 'テーブルを挿入',
	tabledelete : 'テーブルを削除',
	tablecolinsertleft : '左に列を挿入する',
	tablecolinsertright : '右に列を挿入する',
	tablerowinsertabove : '上に行を挿入する',
	tablerowinsertbelow : '下に行を挿入する',
	tablerowmerge : '下にセルをマージする',
	tablecolmerge : '右にセルをマージする',
	tablerowsplit : '行を分割',
	tablecolsplit : '列を分割',
	tablecoldelete : '列を削除',
	tablerowdelete : '行を削除',
	noColor : '色なし',
	pleaseSelectFile : 'ファイルを選択してください。',
	invalidImg : "有効なURLアドレスを入力してください。\ n jpg、gif、bmp、png形式のみが許可されています。",
	invalidMedia : "有効なURLアドレスを入力してください。swf,flv,mp3,wav,wma,wmv,mid,avi,mpg,asf,rm,rmvb形式のみが許可されています。",
	invalidWidth : "幅は数字でなければなりません。",
	invalidHeight : "高さは数字でなければなりません。",
	invalidBorder : "ボーダーは数字でなければなりません。",
	invalidUrl : "有効なURLアドレスを入力してください。",
	invalidRows : '行数は必須項目で、０以上の数字を入力してください。',
	invalidCols : '列数は必須項目で、０以上の数字を入力してください。',
	invalidPadding : 'マージンは数字でなければなりません。',
	invalidSpacing : 'スペースは数字でなければなりません。',
	invalidJson : 'サーバーエラー。',
	uploadSuccess : 'アップロード成功。',
	cutError : 'ブラウザのセキュリティ設定によってカット操作を使用できないので、ショートカットキー（Ctrl + X）を使用してください。',
	copyError : 'ブラウザのセキュリティ設定によってコピー操作をできないので、ショートカットキー（Ctrl + C）を使用してください。',
	pasteError : 'ブラウザのセキュリティ設定によって貼り付け操作をできないので、ショートカットキー（Ctrl + V）を使用してください。',
	ajaxLoading : '読み込み中、お待ちください...',
	uploadLoading : 'アップロード、しばらくお待ちください...',
	uploadError : 'アップロードエラー',
	'plainpaste.comment' : 'ショートカットキー（Ctrl + V）でコンテンツを下のボックスに貼り付けてください。',
	'wordpaste.comment' : 'ショートカットキー（Ctrl + V）でコンテンツを下のボックスに貼り付けてください。',
	'code.pleaseInput' : 'プログラムコードを入力してください。 ',
	'link.url' : 'URL',
	'link.linkType' : 'タイプを開く',
	'link.newWindow' : '新しいウィンドウ',
	'link.selfWindow' : '現在のウィンドウ',
	'flash.url' : 'URL',
	'flash.width' : '幅',
	'flash.height' : '高さ',
	'flash.upload' : 'アップロード',
	'flash.viewServer' : 'ファイルスペース',
	'media.url' : 'URL',
	'media.width' : '幅',
	'media.height' : '高さ',
	'media.autostart' : '自動再生',
	'media.upload' : 'アップロード',
	'media.viewServer' : 'ファイルスペース',
	'image.remoteImage' : 'ネットワークイメージ',
	'image.localImage' : 'ローカルアップロード',
	'image.remoteUrl' : 'イメージアドレス  ',
	'image.localUrl' : 'アップロードファイル',
	'image.size' : 'イメージサイズ',
	'image.width' : '幅',
	'image.height' : '高さ',
	'image.resetSize' : 'リセットサイズ',
	'image.align' : '配置',
	'image.defaultAlign' : 'デフォルト',
	'image.leftAlign' : '左揃え',
	'image.rightAlign' : '右揃え',
	'image.imgTitle' : '画像の説明',
	'image.upload' : 'ブラウズ...',
	'image.viewServer' : 'イメージスペース',
	'multiimage.uploadDesc' : 'ユーザーが<％= uploadLimit％>画像を同時にアップロードできますが、画像の容量は<％= sizeLimit％>を超えることができません',
	'multiimage.startUpload' : 'アップロード',
	'multiimage.clearAll' : 'すべてクリア',
	'multiimage.insertAll' : 'すべて挿入',
	'multiimage.queueLimitExceeded' : 'ファイルの数が上限を超えています。',
	'multiimage.fileExceedsSizeLimit' : 'ファイルサイズが制限を超えています。',
	'multiimage.zeroByteFile' : '空のファイルをアップロードできません。   ',
	'multiimage.invalidFiletype' : 'ファイル形式は正しくありません。',
	'multiimage.unknownError' : 'エラー、アップロードできません。',
	'multiimage.pending' : 'アップロード待ち',
	'multiimage.uploadError' : 'アップロード失敗',
	'filemanager.emptyFolder' : '空のフォルダ',
	'filemanager.moveup' : '前のフォルダに移動',
	'filemanager.viewType' : '表示モード：',
	'filemanager.viewImage' : 'サムネイル',
	'filemanager.listImage' : '詳細',
	'filemanager.orderType' : '並べ替え：',
	'filemanager.fileName' : 'ネーム',
	'filemanager.fileSize' : 'サイズ',
	'filemanager.fileType' : 'タイプ',
	'insertfile.url' : 'URL',
	'insertfile.title' : 'ファイルの説明',
	'insertfile.upload' : 'アップロード',
	'insertfile.viewServer' : 'ファイルスペース',
	'table.cells' : 'セル番号',
	'table.rows' : '行数',
	'table.cols' : '列数',
	'table.size' : 'サイズ',
	'table.width' : '幅',
	'table.height' : '高さ',
	'table.percent' : '%',
	'table.px' : 'px',
	'table.space' : 'テーブルスペース',
	'table.padding' : 'パッディング',
	'table.spacing' : 'スペース',
	'table.align' : '配置',
	'table.textAlign' : '水平配置',
	'table.verticalAlign' : '垂直配置',
	'table.alignDefault' : 'デフォルト',
	'table.alignLeft' : '左揃え',
	'table.alignCenter' : '中央揃え',
	'table.alignRight' : '右揃え',
	'table.alignTop' : 'トップ',
	'table.alignMiddle' : 'ミドル',
	'table.alignBottom' : 'ボトム',
	'table.alignBaseline' : 'ベースライン',
	'table.border' : 'ボーダー',
	'table.borderWidth' : 'ボーダー',
	'table.borderColor' : 'カラー',
	'table.backgroundColor' : '背景色',
	'map.address' : 'アドレス: ',
	'map.search' : '検索',
	'baidumap.address' : 'アドレス: ',
	'baidumap.search' : '検索',
	'baidumap.insertDynamicMap' : 'ダイナミックマップの挿入',
	'anchor.name' : 'アンカー名',
		
	'formatblock.formatBlock' : {
		h1 : '見出し1',
		h2 : '見出し2',
		h3 : '見出し3',
		h4 : '見出し4',
		p : '正 文'
	},
	'fontname.fontName' : {
		'SimSun' : '明朝体',
		'NSimSun' : '新宋体',
		'FangSong_GB2312' : '仿宋_GB2312',
		'KaiTi_GB2312' : '楷書体_GB2312',
		'SimHei' : 'ゴチック体',
		'Microsoft YaHei' : 'Msyh',
		'Arial' : 'Arial',
		'Arial Black' : 'Arial Black',
		'Times New Roman' : 'Times New Roman',
		'Courier New' : 'Courier New',
		'Tahoma' : 'Tahoma',
		'Verdana' : 'Verdana'
	},
	'lineheight.lineHeight' : [
		{'1' : '行間1倍'},
		{'1.5' : '行間1.5倍'},
		{'2' : '行間2倍'},
		{'2.5' : '行間2.5倍'},
		{'3' : '行間3倍'}
	],
	'template.selectTemplate' : 'オプションテンプレート',
	'template.replaceContent' : '現在のコンテンツを置き換える',
	'template.fileList' : {
		'1.html' : 'イメージとテキスト',
		'2.html' : '表',
		'3.html' : '段落番号'
	}
}, 'ja-JP');

KindEditor.options.langType = 'ja-JP';