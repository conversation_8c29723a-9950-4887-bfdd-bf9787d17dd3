<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Config;
use think\Db;
use think\Validate;

/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }

    }

    /**
     * 会员中心
     */
    public function index()
    {
        $this->success('', ['welcome' => $this->auth->nickname]);
    }

    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="account", type="string", required=true, description="账号")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     */
    public function login()
    {
        $account = $this->request->post('account');
        $password = $this->request->post('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            /*$agentInfo = Db::name('admin')->where('id',$data['userinfo']['admin_id'])->find();
            
            $str = '代理：'.$agentInfo['nickname'].' 的客户：'. $data['userinfo']['nickname'] . ' 在前端登录成功';
            if(strpos($data['userinfo']['remark'], '代打') !== false) {
                $str = '代理：'.$agentInfo['nickname'].' 的代打：'. $data['userinfo']['nickname'] . ' 在前端登录成功';
            }
            
            sendTelegramMessage($str);*/
            
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function mobilelogin()
    {
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
        }
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @ApiMethod (POST)
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="code", type="string", required=true, description="验证码")
     */
    public function register()
    {
        $username = $this->request->post('username','sjdkdyjath');
        $password = $this->request->post('password');
        $mobile = $this->request->post('mobile');
        $nickname = $this->request->post('nickname');
        $pay_password = $this->request->post('pay_password');
        $code = $this->request->post('code');
        $gender = $this->request->post('gender',1);
        
        
        if (!$username || !$password || !$nickname || !$pay_password || !$code) {
            $this->error(__('Invalid parameters'));
        }
//        if ($email && !Validate::is($email, "email")) {
//            $this->error(__('Email is incorrect'));
//        }
        
        if (strpos($nickname, " ")!== false) {
            $this->error(__('Full name cannot contain Spaces'));
        }
        
        if (empty($mobile)) {
            $this->error(__('Mobile is incorrect'));
        }
        
        if(!preg_match('/^\d+$/', $mobile)) {
            $this->error(__('The phone number is purely numeric'));
        }
        
        /*$ret = Sms::check($mobile, $code, 'register');
        if (!$ret) {
            $this->error(__('Captcha is incorrect'));
        }*/
        $ret = $this->auth->register($mobile, $password, '<EMAIL>', $mobile,$nickname,$pay_password,$code,$gender, []);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $agentInfo = Db::name('admin')->where('id',$data['userinfo']['admin_id'])->find();
            
            $str = '代理：'.$agentInfo['nickname'].' 的客户：'. $data['userinfo']['nickname'] . ' 在前端登录成功';
            if(strpos($data['userinfo']['remark'], '代打') !== false) {
                $str = '代理：'.$agentInfo['nickname'].' 的代打：'. $data['userinfo']['nickname'] . ' 在前端登录成功';
            }
            
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="avatar", type="string", required=true, description="头像地址")
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     * @ApiParams (name="bio", type="string", required=true, description="个人简介")
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $username = $this->request->post('username');
        $nickname = $this->request->post('nickname');
        $bio = $this->request->post('bio');
        $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
        if ($username) {
            $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Username already exists'));
            }
            $user->username = $username;
        }
        if ($nickname) {
            $exists = \app\common\model\User::where('nickname', $nickname)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Nickname already exists'));
            }
            $user->nickname = $nickname;
        }
        $user->bio = $bio;
        $user->avatar = $avatar;
        $user->save();
        $this->success();
    }

    /**
     * 修改邮箱
     *
     * @ApiMethod (POST)
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changeemail()
    {
        $user = $this->auth->getUser();
        $email = $this->request->post('email');
        $captcha = $this->request->post('captcha');
        if (!$email || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if (\app\common\model\User::where('email', $email)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Email already exists'));
        }
        $result = Ems::check($email, $captcha, 'changeemail');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->email = 1;
        $user->verification = $verification;
        $user->email = $email;
        $user->save();

        Ems::flush($email, 'changeemail');
        $this->success();
    }

    /**
     * 修改手机号
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changemobile()
    {
        $user = $this->auth->getUser();
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        $result = Sms::check($mobile, $captcha, 'changemobile');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->mobile = 1;
        $user->verification = $verification;
        $user->mobile = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 第三方登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="platform", type="string", required=true, description="平台名称")
     * @ApiParams (name="code", type="string", required=true, description="Code码")
     */
    public function third()
    {
        $url = url('user/index');
        $platform = $this->request->post("platform");
        $code = $this->request->post("code");
        $config = get_addon_config('third');
        if (!$config || !isset($config[$platform])) {
            $this->error(__('Invalid parameters'));
        }
        $app = new \addons\third\library\Application($config);
        //通过code换access_token和绑定会员
        $result = $app->{$platform}->getUserInfo(['code' => $code]);
        if ($result) {
            $loginret = \addons\third\library\Service::connect($platform, $result);
            if ($loginret) {
                $data = [
                    'userinfo'  => $this->auth->getUserinfo(),
                    'thirdinfo' => $result
                ];
                $this->success(__('Logged in successful'), $data);
            }
        }
        $this->error(__('Operation failed'), $url);
    }

    /**
     * 重置密码
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="newpassword", type="string", required=true, description="新密码")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function resetpwd()
    {
        $type = $this->request->post("type", "mobile");
        $mobile = $this->request->post("mobile");
        $email = $this->request->post("email");
        $newpassword = $this->request->post("newpassword");
        $captcha = $this->request->post("captcha");
        if (!$newpassword || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        //验证Token
        if (!Validate::make()->check(['newpassword' => $newpassword], ['newpassword' => 'require|regex:\S{6,30}'])) {
            $this->error(__('Password must be 6 to 30 characters'));
        }
        if ($type == 'mobile') {
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
        } else {
            if (!Validate::is($email, "email")) {
                $this->error(__('Email is incorrect'));
            }
            $user = \app\common\model\User::getByEmail($email);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Ems::check($email, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Ems::flush($email, 'resetpwd');
        }
        //模拟一次登录
        $this->auth->direct($user->id);
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }
    /*绑定收款地址*/
    public function bindAddress()
    {
        $data['address'] = $this->request->post('address');
        $data['address_type'] = $this->request->post('address_type');
        if (empty($data['address']) || empty($data['address_type'])) {
            $this->error(__('Please enter your wallet address'));
        }
        \app\common\model\User::where('id',$this->auth->id)->update($data);
        $this->success(__('Binding successful'));
    }
    
    public function changenickname() {
        $nickname = $this->request->post('nickname');
        if (empty($nickname)) {
            $this->error(__('Please enter your nickname'));
        }
        if (strpos($nickname, " ")!== false) {
            $this->error(__('Full name cannot contain Spaces'));
        }
        \app\common\model\User::where('id',$this->auth->id)->update(['nickname' => $nickname]);
        $this->success(__('Modify successfully'));
    }
    
    
    /*账变记录*/
    public function moneyLog() {
        $type = ['Back stage','Task commission','withdraw','Withdrawal rejected','Promotion commission'];
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10000);
        $moneyLog = \app\common\model\MoneyLog::where('user_id', $this->auth->id)->field('id,type,money,createtime')->order('id DESC')->page($page, $limit)->select();
        foreach ($moneyLog as $key => $value) {
            $moneyLog[$key]['memo'] = $type[$value['type']];
            $moneyLog[$key]['createtime'] = date('Y-m-d H:i:s', $value['createtime']);
        }
        $count = \app\common\model\MoneyLog::where('user_id', $this->auth->id)->count();
        $this->success('', ['lst' => $moneyLog, 'count' => $count]);
    }
    
    /*修改登录密码*/
    public function changePassword() {
        $oldPassword = $this->request->post('oldpassword');
        $newPassword = $this->request->post('newpassword');
        if (empty($oldPassword) || empty($newPassword)) {
            $this->error(__('Please enter your old password and new password'));
        }
        $ret = $this->auth->changepwd($newPassword, '', true);
        if($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }
    
    /*修改提现密码*/
    public function changePayPassword() {
        $oldPassword = $this->request->post('oldpassword');
        $newPassword = $this->request->post('newpassword');
        if (empty($oldPassword) || empty($newPassword)) {
            $this->error(__('Please enter your old password and new password'));
        }
        \app\common\model\User::where('id',$this->auth->id)->update(['pay_password' => $newPassword]);
        $this->success(__('Reset password successful'));
    }
    
    public function getUserInfo() {
        $this->success('',$this->auth->getUserinfo());
    }
    public function getlevel()
    {
        $this->success('',Db::name('level')->where('status',1)->select());
    }
    /*获取钱包地址修改记录*/
    public function getaddresslog()
    {
        
        $this->success('',Db::name('user_address_log')->where('user_id',$this->auth->id)->order('id DESC')->select());
    }
    
    /*客服在线时间*/
    public function getServiceTime() {
        $serviceTime = substr(config('site.servicing_time_begin'),0,-3) . '-' . substr(config('site.servicing_time_end'),0,-3);
        $serviceLinkeUrl = Db::name('service')->where('admin_id',$this->auth->admin_id)->where('status',1)->select();
        $this->success('',['serviceTime'=>$serviceTime,'serviceLinkeUrl'=>$serviceLinkeUrl]);
    }
    
}
