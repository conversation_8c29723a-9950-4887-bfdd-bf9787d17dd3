<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id|htmlentities}">
    <div class="form-group">
        <label for="c-username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text" value="{$row.username|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-password" data-rule="password" class="form-control" name="row[password]" type="password" value="" placeholder="{:__('Leave password blank if dont want to change')}" autocomplete="new-password" />
        </div>
    </div>

    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-mobile" data-rule="" class="form-control" name="row[mobile]" type="text" value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Pay_password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-pay_password" data-rule="" class="form-control" name="row[pay_password]" type="text" value="{$row.pay_password|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Level')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input data-rule="" class="form-control" type="text" disabled value="{$user_level|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="c-money" class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-money" data-rule="required" class="form-control" name="row[money]" type="number" value="{$row.money|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Withdraw')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input data-rule="" class="form-control" type="text" disabled value="{$withdraw|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('RechargeAmount')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input data-rule="" class="form-control" type="text" disabled value="{$rechargeAmount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-money" class="control-label col-xs-12 col-sm-2">{:__('Credit_score')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-credit_score" data-rule="required" class="form-control" name="row[credit_score]" type="number" value="{$row.credit_score|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-address" data-rule="required" class="form-control" name="row[address]" type="text" value="{$row.address|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Address_type')}:</label>
        <div class="col-xs-12 col-sm-4">
            {:build_select('row[address_type]',[''=>'请选择钱包区块链','ERC20'=>__('ERC20'), 'TRC20'=>__('TRC20')],$row['address_type'])}
        </div>
    </div>



    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Joint_order_setup')}:</label>
        <div class="col-xs-12 col-sm-4" id="input_box">
            <dl class="fieldlist" data-name="row[configgroup]">
                <dd>
                <ins>键名</ins>
                <ins>键值</ins>
                <ins>键值</ins>
                <ins>键值</ins>
                </dd>
                <dd>
                <a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a>
                </dd>
                <textarea name="row[configgroup]" class="form-control hide" cols="30" rows="5">{"basic":"基础配置","email":"邮件配置","dictionary":"字典配置","user":"会员配置","example":"示例分组"}</textarea>
            </dl>
        </div>
    </div>
    <div id="btn">添加</div>
    <div id="btn"></div>
    <button id="getValuesBtn">获取输入值</button>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
<style>
    #input_box {
        display: flex;
        flex-wrap: wrap;
        input {
            width: 25%;
        }
    }
</style>
<script>
    const oUl = document.getElementById('input_box');
    const oBtn = document.getElementById('btn');
    const getValuesBtn = document.getElementById('getValuesBtn');
    const inputGroupsArray = []; // 创建一个数组来存储每个 div
    function createInputGroup() {
        const div = document.createElement('div');
        div.className = 'input-container';

        const inputs = [];
        for (let i = 0; i < 4; i++) {
            const oLi = document.createElement('input');
            oLi.className = 'form-control';
            oLi.name = ''
            oLi.type = 'number';
            div.appendChild(oLi);

            inputs.push(oLi); // 将输入框添加到数组中
        }

        const deleteBtn = document.createElement('button');
        deleteBtn.textContent = '删除';
        deleteBtn.onclick = function () {
            oUl.removeChild(div);
            const index = inputGroupsArray.indexOf(div);
            if (index > -1) inputGroupsArray.splice(index, 1); // 从数组中删除
        };

        div.appendChild(deleteBtn);
        oUl.appendChild(div);
        inputGroupsArray.push(div); // 将 div 添加到数组中
    }

    oBtn.onclick = function () {
        console.log(inputGroupsArray)
        createInputGroup();
    };
    getValuesBtn.onclick = function () {
        const values = inputGroupsArray.map(group => {
            const inputs = group.getElementsByTagName('input');
            return Array.from(inputs).map(input => input.value);
        });
        console.log(values);
    };
    createInputGroup();
</script>