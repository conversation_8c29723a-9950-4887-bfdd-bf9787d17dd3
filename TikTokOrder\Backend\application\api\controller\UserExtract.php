<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\helper\Time;
use think\Cache;

/**
 * 会员提现
 */
class UserExtract extends Api
{
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = [];
    
    public function withdraw() {
        $money = $this->request->post('money');
        $payPassword = $this->request->post('pay_password');
        $uid = $this->auth->id;
        $user = Db::name('user')->where('id',$uid)->find();
        // 判断是否开启全天服务
        if(config('site.open_all_day') != 1) {
            /*判断现在是否在非工作时间*/
            $currentTime  = date('H');
            $beginTime = substr(config('site.servicing_time_begin'),0,-6);
            $endTime = substr(config('site.servicing_time_end'),0,-6);
            if ($currentTime < $beginTime || $currentTime >= $endTime) {
                $this->error(__('Daily withdrawal hours are from %dam to %dpm',$beginTime,$endTime));
            }
        }
        // 判断是否是同一组提交
        if(!Cache::get('form_processing_'.$user['id'])) {
            Cache::set('form_processing_'.$user['id'],time(),20);
        } else {
            $this->error('Withdrawal is being processed, please do not submit again!');
        }
        
        if(Db::name('user_task')->where('user_status',0)->where('user_id',$user['id'])->find()) {
            $this->error(__('Please finish the unfinished task first'));
        }
        
        $withdraw_min = config('site.withdraw_min');
        if($money < $withdraw_min) {
            $this->error(__('The minimum withdrawal amount on the platform is %d',$withdraw_min));
        }
        
        if (empty($user['address'])) {
            $this->error(__('Please set up the withdrawal address'));
        }
        
        if ($user['pay_password'] != $payPassword) {
            $this->error(__('Pay password error'));
        }
        if ($user['money'] < $money) {
            $this->error(__('Insufficient balance'));
        }
        
        /*检查今日会员任务组数*/
        /*list($start, $end) = Time::today();
        $task_group_count = Db::name('user_task')->where('user_id',$user['id'])->where('createtime','between',[$start,$end])->count('DISTINCT task_group_id');
        if ($task_group_count < 2) {
            $this->error(__('You need to complete two sets of tasks per day before you can withdraw cash'));
        }*/
        $fee = bcmul(config('site.fee'),$money,2);
        $fee = bcmul($fee,0.01,2);
        if ($user['address_type'] == 'TRC20') {
            $addressType = 'Tron';
        } else {
            $addressType = 'Eth';
        }
        $order = [
            'user_id' => $user['id'],
            'order_sn' => 'TX'.date('YmdHis').mt_rand(1000,9999),
            'amount' => $money,
            'fee' => $fee,
            'recieved_amount' => bcsub($money,$fee,2),
            'address' => $user['address'],
            'address_type' => $addressType,
            'createtime' => time(),
        ];
        $log = [
            'user_id' => $user['id'],
            'type' => 2,
            'money' => -$money,
            'before' => $user['money'],
            'after' => bcsub($user['money'],$money,2),
            'memo' => '会员提现',
            'createtime' => time()
        ];
        Db::startTrans();
        try {
            Db::name('user_money_log')->insert($log);
            Db::name('withdraw')->insert($order);
            Db::name('user')->where('id',$user['id'])->setDec('money',$money);;
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error(__('Withdrawal failed, please try again'));
        }
        // 发送提现消息
        // $agentInfo = Db::name('admin')->where('id',$user['admin_id'])->find();
        
        // $str = '代理：'.$agentInfo['nickname'].' 的客户：'. $user['nickname'] .' 申请出款：'.$money;
        // if(strpos($user['remark'], '代打') !== false) {
        //     $str = '代理：'.$agentInfo['nickname'].' 的代打：'. $user['nickname'] .' 申请出款：'.$money;
        // }
        
        // sendTelegramMessage($str);
        
        // 删除缓存
        Cache::rm('form_processing_'.$user['id']);
        
        $this->success(__('Successfully submit withdrawal. Please wait our customer support to deposit toyourwallet address'));
    }
    
    /*获取提现列表*/
    public function withdrawList(){
        
        $page = $this->request->post('page');
        $limit = $this->request->post('limit');
        $data['lst'] = Db::name('withdraw')->where('user_id',$this->auth->id)->page($page,$limit)->order('id DESC')->select();
        foreach($data['lst'] as $key => $value) {
            $data['lst'][$key]['createtime'] = date('Y-m-d H:i:s',$value['createtime']);
        }
        
        $data['count'] = Db::name('withdraw')->where('user_id',$this->auth->id)->count();
        $this->success('',$data);
    }
}
