<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

/**
 * 定时任务
 */
class Reset extends Api
{
    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    
    /*重置会员每日完成任务数*/
    public function resetTaskCount() {
        
        $h = date('H');
        if ($h > 0) {
            return '时间不对，当前时间为：'.$h . '|' . date('Y-m-d H:i:s'); // 非0点不执行重置任务数
        }
        // 重置会员信用分
        Db::name('user')->where('task_number','>=',80)->where('credit_score','<',100)->setInc('credit_score',10);
        Db::name('user')->where('task_number','<',80)->where('credit_score','>',0)->setDec('credit_score',10);
        // 重置今日任务数量
        Db::name('user')->where('task_number','>',0)->update(['task_number'=>0]);
        return 'OK';
    }
    
}
