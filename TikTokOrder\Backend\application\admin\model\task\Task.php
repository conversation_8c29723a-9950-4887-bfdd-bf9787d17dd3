<?php

namespace app\admin\model\task;

use think\Model;
use traits\model\SoftDelete;

class Task extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'task';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    public function getStatusList()
    {
        return ['0' => __('Type 0'), '1' => __('Type 1')];
    }

    







}
