$dialogWidth:652px;
$dialogZindex:811213;
$fontsize:14px;
.uedbody{
	div, dl, dt, dd, ul, li,ol, h1, h2, h3, h4, h5, h6, pre, code, form, fieldset, legend, button, textarea, blockquote,p{margin:0; padding:0;}
	h1,h2,h3,h4,h5,h6 {font-weight:normal;}
	li{list-style-type:none;}
	ol,ul,dl{list-style:none;}
	em{font-style:normal;}
	img{border:none;vertical-align:middle;}
	select,label,input{vertical-align:middle; padding:0; margin:0;outline:medium;font-size:12px;}
	textarea{resize: none; line-height:18px;}
	table { border-collapse: collapse; border-spacing: 0; empty-cell:show;}
	a{text-decoration:none;color:#333;outline:none; cursor:pointer;}
	a:hover{cursor:pointer;blr:expression(this.onFocus=this.blur());}

	box-sizing : content-box !important;
	-webkit-box-sizing: content-box !important;
	-moz-box-sizing: content-box !important;

	* {
		padding: 0; margin: 0;
		box-sizing : content-box !important;
		-webkit-box-sizing: content-box !important;
		-moz-box-sizing: content-box !important;
	}

	.clearfix:before, .clearfix:after {
		content: "";
		display: table;
	}
	.clearfix:after{
		clear: both;
	}
	.clearfix{
		*zoom: 1;
	}
	.fr{float: right;}
	.fl{float: left;}

	width: $dialogWidth;
	position: fixed;
	padding: 2px 0px 0px 2px;
	background-color: #FFF;
	border: 1px solid rgba(0, 0, 0, 0.2);
	border-radius: 6px;
	box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.2);
	z-index: $dialogZindex;
	font-family:"微软雅黑";
	font-size: 12px;
	margin: 0px auto;

	//start of ued_title
	.ued_title{
		height: 26px;
		border-bottom: 1px solid #C6C6C6;
		background: transparent url("images/dialog-title-bg.png") repeat-x scroll 0% 0%;
		position: relative;
		cursor: move;

		.icon{background: url("images/icons-all.gif") no-repeat center;}

		.uedbar {
			span{
				font-weight: bold;
				font-size: 14px;
				color: #444;
				line-height: 26px;
				padding-left: 5px;
			}
		}

		.close_btn{
			height: 20px;
			width: 20px;
			cursor: pointer;
			background-position :0px -59px;
			position: absolute;
			right: 5px;
			top: 3px;

			&:hover{background-position: 0px -89px;}
		}
	}
	//end of ued_title

	//button styles
	.btn {
		display: inline-block;
		margin-bottom: 0px;
		margin-right: 5px;
		padding: 4px 10px;
		font-weight: 400;
		text-align: center;
		cursor: pointer;
		border: 1px solid transparent;
		white-space: nowrap;
		font-size: $fontsize;
		border-radius: 3px;
		-moz-user-select: none;
		box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
		padding: 5px 14px;
	}
	.btn-default {
		border: 1px solid #CFCFCF;
		color: #565656;
		background: #ffffff;
		&:hover {
			background: #f0f0f0;
		}
	}
	.btn-primary {
		background:#00b7ee;
		color: #ffffff;
		&:hover {
			background: #00a2d4;
		}
	}//button style end
	.btn.disabled {
		cursor: not-allowed;
		opacity: .65;
		&:hover {
			background: #00b7ee;
		}
	}

	//start of wrapper
	.wrapper {
		width: 630px;
		height: 380px;
		margin: 0px auto;
		padding: 10px;
		position: relative;
		font-family: sans-serif;

		//start of wra_head
		.wra_head {
			span {
				float: left;
				padding: 0px 5px;
				margin-right:3px;
				height: 30px;
				border: 1px solid #CCC;
				background: transparent url("images/dialog-title-bg.png") repeat-x scroll 0% 0%;
				text-align: center;
				line-height: 30px;
				cursor: pointer;
				position: relative;
			}
			span.focus {
				height: 31px;
				border-bottom: medium none;
				background: #FFF none repeat scroll 0% 0%;
				position: relative;
				z-index: 2;
			}
		}//end of wra_head

		//start of wra_body
		.wra_body{

			width: 100%;
			height: 346px;
			clear: both;
			margin: 0px auto;
			border: 1px solid #CCC;
			display: block;
			clip: auto;
			overflow: hidden;
			top: -1px;
			position: relative;

			//upload panel start
			.upload-panel {
				.wra_pla {
					zoom: 1; overflow: hidden;
					text-align: center;

					.upload-image-placeholder {
						margin: 10px;
						border: 2px dashed #e6e6e6;
						height: 172px;
						padding-top: 150px;
						text-align: center;
						background: url("images/image.png") center 70px no-repeat;
						position: relative;
						top: 0;

						.webuploader-element-invisible {
							//width: 145px;
							//height: 40px;
							//position: absolute;
							//left: 225px;
							opacity: 0;
							width: 0; height: 0;
						}

						.image-select {
							padding: 10px 30px;
							cursor: pointer;
						}
					}
				}

				//image list box start
				.image-list-box {
					.wra_bar {
						border-bottom: 1px solid #DADADA;
						padding: 8px;
						vertical-align: middle;
						position: relative;
						zoom: 1;  overflow: hidden;

						.info{
							font-size: $fontsize;
							color: #666;
							margin-top: 6px;
						}

						.fr {
							.btn {padding: 6px 15px;}
						}

					}

					//filelist start
					.filelist{
						list-style: outside none none;
						margin: 0px;
						padding: 0px;
						overflow-x: hidden;
						overflow-y: auto;
						position: relative;
						height: 300px;

						li {
							width: 113px;
							height: 113px;
							margin: 9px 0px 0px 9px;
							position: relative;
							display: block;
							float: left;
							overflow: hidden;
							font-size: 12px;

							.imgWrap{
								position: relative;
								vertical-align: middle;
								text-align: center;
								overflow: hidden;
								width: 113px;
								height: 113px;
								transform-origin: 50% 50% 0px;
								transition: all 200ms ease-out 0s;

							}

							//file opt box start
							.file-opt-box{
								position: absolute;
								display: none;
								background: rgba(0, 0, 0, 0.5) none repeat scroll 0% 0%;
								width: 100%;
								top: 0px;
								left: 0px;
								overflow: hidden;

								span {
									width: 24px;
									height: 24px;
									display: inline;
									float: right;
									text-indent: -9999px;
									overflow: hidden;
									background: url("images/icons.png") no-repeat;
									margin: 5px 1px 1px;
									cursor: pointer;
									-webkit-tap-highlight-color: rgba(0,0,0,0);
									-webkit-user-select: none;
									-moz-user-select: none;
									-ms-user-select: none;
									user-select: none;
								}

								.remove{
									background-position: -48px -24px;
									&:hover{
										background-position: -48px -0px;
									}
								}
								.rotateRight{
									display: none;
									background-position: -24px -24px;
									&:hover{
										background-position: -24px -0px;
									}
								}
								.rotateLeft{
									display: none;
									background-position: 0px -24px;
									&:hover{
										background-position: 0px 0px;
									}
								}
							}//file opt box end

							.success{
								background: url("images/success.png") no-repeat right;
								position: absolute;
								display: none;
								left: 0px;
								bottom: 0px;
								height: 40px;
								width: 100%;
							}

							.error {
								position: absolute;
								width: 100%;
								display: none;
								background: rgba(255, 255, 255, 0.7) none repeat scroll 0% 0%;
								left: 0;
								padding:5px 3px;
								color: #cc0000;
								border: 1px solid #cccccc;
								bottom: 0;
							}

							.progress {
								position: absolute;
								width: 100%;
								bottom: 0;
								left: 0;
								bottom: 0;
								height: 8px;
								overflow: hidden;
								z-index: 50;
								margin: 0;
								border-radius: 0;
								background: none;
								-webkit-box-shadow: 0 0 0;

								span {
									width: 0;
									height: 100%;
									background: #1483d8 url("images/progress.png") repeat-x;
									-webit-transition: width 200ms linear;
									-moz-transition: width 200ms linear;
									-o-transition: width 200ms linear;
									-ms-transition: width 200ms linear;
									transition: width 200ms linear;
									-webkit-animation: progressmove 2s linear infinite;
									-moz-animation: progressmove 2s linear infinite;
									-o-animation: progressmove 2s linear infinite;
									-ms-animation: progressmove 2s linear infinite;
									animation: progressmove 2s linear infinite;
									-webkit-transform: translateZ(0);
								}
							}
						}//end li

					}//filelist end

				}//image list box end
			}//upload panel end

			//image online start
			.online{
				width: 100%;
				height: 336px;
				padding: 10px 0px 0px;
				display: none;

				.imagelist {
					width: 100%;
					height: 100%;
					overflow-x: hidden;
					overflow-y: auto;
					position: relative;

					.list {
						li {
							float: left;
							display: block;
							list-style: outside none none;
							padding: 0px;
							width: 113px;
							height: 113px;
							margin: 0px 0px 9px 9px;
							background-color: #EEE;
							overflow: hidden;
							cursor: pointer;
							position: relative;

							img{
								cursor: pointer;
								max-height: 100%;
								max-width: 100%;
								width: auto;
								height: auto;
								position: absolute;
								top: 0;
								bottom: 0;
								left: 0;
								right: 0;
								margin: auto;
							}

							span.ic {
								position: absolute;
								top: 0px;
								left: 0px;
								cursor: pointer;
								width: 113px;
								height: 113px;

								.img-size {
									font-size:12px;
									padding: 5px;
									background:rgba(0,0,0,0.6);
									display: none;
									color: #ffffff;
								}

								&:hover{
									width: 107px;
									height: 107px;
									border: 3px solid #1094FA;
									background-position: 72px 72px;
									.img-size {
										display: inline-block;
										width: 97px;
									}

								}
							}
							span.selected {
								background: url("images/success.png") no-repeat 75px 75px;
							}
						}
					}

				}

			} //image online end

			//image search start
			.searchbox {
				width: 100%;
				padding: 10px 0px 0px;
				zoom: 1; overflow: hidden;
				display: none;

				//搜索条
				.search-bar {

					.searTxt{
						margin-left: 5px;
						background: #FFF none repeat scroll 0% 0%;
						width: 300px;
						height: 21px;
						line-height: 21px;
						padding: 3px 6px;
						font-size: $fontsize;
						line-height: 1.42857;
						border: 1px solid #CCC;
						border-radius: 4px;
						box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.075) inset;
						transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
						margin-right:10px;
					}

					.searchBtn {
						color: #FFF;
						border-color: #285E8E;
						background-color: #3B97D7;
					}
					.searchRese{
						color: #333;
						border-color: #CCC;
						background-color: #FFF;
					}
				}

				.search-imagelist-box {
					width: 100%;
					height: 292px;
					overflow-x: hidden;
					overflow-y: auto;
					position: relative;
					margin-top:10px;

					.search-list {
						zoom:1; overflow: hidden;
						li {
							float: left;
							display: block;
							list-style: outside none none;
							padding: 0px;
							width: 113px;
							height: 113px;
							margin: 0px 0px 9px 9px;
							background-color: #EEE;
							overflow: hidden;
							cursor: pointer;
							position: relative;

							img {
								cursor: pointer;
							}

							span.ic {
								position: absolute;
								top: 0px;
								left: 0px;
								cursor: pointer;
								width: 113px;
								height: 113px;

								.img-size {
									font-size:12px;
									padding: 5px;
									background:rgba(0,0,0,0.6);
									display: none;
									color: #ffffff;
								}

								&:hover {
									width: 107px;
									height: 107px;
									border: 3px solid #1094FA;
									background-position: 72px 72px;
									overflow: hidden;
									.img-size {
										display: inline-block;
										width: 97px;
									}
								}
							}
							span.selected {
								background: url("images/success.png") no-repeat 75px 75px;
							}
						}
					}
				}

			}//image search start end

			.no-data {
				line-height: 40px;
				height: 40px;
				font-size:$fontsize;
				color: #999999;
				text-align: center;
				display: none;
				.error {
					color: #cc0000;
				}
			}

			.loading-icon {
				background:rgba(0,0,0,0.4) url("images/loader.gif") no-repeat center center;
				position: absolute;
				width: 100%;
				height: 346px;
				top:0; left: 0;
				display: none;
				text-align: center;

				.loading-message {
					position: absolute;
					font-size: 14px;
					color: #f0f0f0;
					top: 210px;
					left:253px;
				}
			}

			//图片占位符
			.icon-placeholder {
				background-size: 113px 113px;
				display: block;
				width: 113px;
				height: 113px;
			}
			.icon-aep, .icon-aepx {background-image: url("icons/aep.png");}
			.icon-asp {background-image: url("icons/asp.png");}
			.icon-avi {background-image: url("icons/avi.png");}
			.icon-c, .icon-cpp {background-image: url("icons/c.png");}
			.icon-conf {background-image: url("icons/conf.png");}
			.icon-css {background-image: url("icons/css.png");}
			.icon-htm,.icon-html {background-image: url("icons/htm.png");}
			.icon-default {background-image: url("icons/txt.png");}
			.icon-doc,.icon-docx {background-image: url("icons/doc.png");}
			.icon-eps {background-image: url("icons/eps.png");}
			.icon-fla,.icon-swf {background-image: url("icons/fla.png");}
			.icon-idn {background-image: url("icons/idn.png");}
			.icon-ini {background-image: url("icons/ini.png");}
			.icon-java, .icon-jar, .icon-war {background-image: url("icons/java.png");}
			.icon-js {background-image: url("icons/js.png");}
			.icon-jsf {background-image: url("icons/jsf.png");}
			.icon-md, .icon-markdown {background-image: url("icons/markdown.png");}
			.icon-mdb {background-image: url("icons/mdb.png");}
			.icon-midi {background-image: url("icons/midi.png");}
			.icon-mov {background-image: url("icons/mov.png");}
			.icon-mp3 {background-image: url("icons/mp3.png");}
			.icon-mpeg {background-image: url("icons/mpeg.png");}
			.icon-pdf {background-image: url("icons/pdf.png");}
			.icon-php {background-image: url("icons/php.png");}
			.icon-ppt, .icon-pptx {background-image: url("icons/ppt.png");}
			.icon-psd {background-image: url("icons/psd.png");}
			.icon-pst {background-image: url("icons/pst.png");}
			.icon-pub {background-image: url("icons/pub.png");}
			.icon-py {background-image: url("icons/py.png");}
			.icon-rb {background-image: url("icons/rb.png");}
			.icon-rm, .icon-rmvb {background-image: url("icons/rmvb.png");}
			.icon-scss {background-image: url("icons/scss.png");}
			.icon-tif {background-image: url("icons/tif.png");}
			.icon-txt {background-image: url("icons/txt.png");}
			.icon-vsd {background-image: url("icons/vsd.png");}
			.icon-wav {background-image: url("icons/wav.png");}
			.icon-wma {background-image: url("icons/wma.png");}
			.icon-wmv {background-image: url("icons/wmv.png");}
			.icon-xls, .icon-xlsx {background-image: url("icons/xls.png");}
			.icon-xml {background-image: url("icons/xml.png");}
			.icon-zip, .icon-rar, .icon-tgz, .icon-gz, .icon-tar, .icon-7z {background-image: url("icons/zip.png");}

		}//end of wra_body

		.wra_body_server {
			height: 380px;

			.online {
				display: block !important;
				height: 365px;
			}
		}

	}//start of wrapper

	.wra-btn-group {
		clear: both;
		zoom: 1;
		text-align: right;
		padding: 10px 20px 20px 0px;

		.tip-text {
			float: left;
			padding: 5px 10px;
			color: #999;
			font-size: 14px;
		}
	}
}

@media (max-width: $dialogWidth) {
	.uedbody {
		width: 100%;
		.wrapper {
			.wra_body .online .imagelist .list li {
				margin:0px 0px 5px 5px;
			}
			width: calc(100% - 20px);
		}
		.wra-btn-group {
			padding: 10px 10px 10px 0px;
		}
	}
}
