<?php

namespace app\admin\controller\user;

use app\common\controller\Backend;
use app\common\library\Auth;
use app\common\model\MoneyLog;
use fast\Random;
use think\Db;
use think\Log;
/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class User extends Backend
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\User;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            // $adminIDs = Db::name('admin')->column('id'); // 这行原被注释，保持注释，因为超管不应默认受限

            $group_id = Db::name('auth_group_access')->where('uid',$this->auth->id)->value('group_id');

            $whereN = []; // 初始化 $whereN 数组

            if($group_id != 1) { // 如果不是超级管理员组 (通常 group_id 1 是超管组)
                $currentAdminAndSubordinates = $this->_getSubordinateAdminIds($this->auth->id);
                if (empty($currentAdminAndSubordinates)) {
                    // 如果没有任何下属且自身ID也未获取到（理论上_getSubordinateAdminIds会包含自身），则无权查看任何用户
                    $whereN['admin_id'] = -1; // 设置一个不可能匹配到的ID，确保查询安全
                } else {
                    $whereN['admin_id'] = ['in', $currentAdminAndSubordinates];
                }
            } else {
                // 超级管理员组：默认不添加 admin_id 的筛选条件, 除非 $filter 中有明确的 admin_id
                // $adminIDs 在这里可以不用特别处理，因为 $whereN['admin_id'] 将由 $filter 控制 (如果 $filter 中有 admin_id)
            }

            $filter = json_decode($this->request->get('filter',''),1);

            if (isset($filter['id']) && $filter['id'] > 0) {
                $whereN['id'] = ['=',$filter['id']];
            }

            if (isset($filter['agentUsername'])) {
                $ids = Db::name('user')->whereLike('nickname','%' . $filter['agentUsername'] . '%')->column('id');
                if(!empty($ids)) {
                    $whereN['agent_id'] = ['in',$ids];
                }
            }

            if (isset($filter['nickname'])) {
                $whereN['nickname'] = ['like','%' . $filter['nickname'] . '%'];
            }

            if (isset($filter['mobile'])) {
                $whereN['mobile'] = ['like','%' . $filter['mobile'] . '%'];
            }

            if (isset($filter['money'])) {
                $money = explode(',',$filter['money']);
                $whereN['money'] = ['between',$money];
            }

            if (isset($filter['status'])) {
                $whereN['status'] = ['=',$filter['status']];
            }

            if (isset($filter['invitation_code'])) {
                $whereN['invitation_code'] = ['=',$filter['invitation_code']];
            }

            if (isset($filter['remark'])) {
                $whereN['remark'] = ['like', '%' . $filter['remark'] . '%'];
            }

            // dump($whereN);die();

            $list = $this->model
                ->where($whereN)
                ->order($sort, $order)
                ->paginate($limit);
            $today = strtotime(date('Y-m-d',time()));
            foreach ($list as $k => $v) {
                $v->avatar = $v->avatar ? cdnurl($v->avatar, true) : letter_avatar($v->nickname);
                $v->admin_nickname = Db::name('admin')->where('id',$v->admin_id)->value('nickname');
                $v->commission = Db::name('user_task')->where('createtime','>=',$today)->where('user_id',$v->id)->where('user_status',1)->sum('commission');
                $v->task_complete_status = $this->getTask($v->id);
                $v->taskStatus = $this->getTaskStatus($v->id);
                $v->agentUsername = Db::name('user')->where('id',$v->agent_id)->value('nickname');
                $v->createtime_ = date('Y-m-d H:i:s',$v->createtime);
                $v->task_number = Db::name('user_task')->where('createtime','>=',$today)->where('user_id',$v->id)->where('user_status',1)->count();

                // --- 修改后的"差额"计算逻辑 ---
                $v->balance_difference = '-'; // 默认值

                // 只有当任务状态为 "未完成" 时才进行差额计算
                if ($v->task_complete_status === '未完成') {
                    // 1. 获取任务完成数
                    $task_status_parts = explode(' / ', $v->taskStatus);
                    $current_completion_count = -1;
                    if (count($task_status_parts) === 2 && is_numeric(trim($task_status_parts[0]))) {
                        $current_completion_count = intval(trim($task_status_parts[0]));
                    }

                    if ($current_completion_count !== -1 && !empty($v->joint_order_setup)) {
                        $joint_order_rules = json_decode($v->joint_order_setup, true);
                        if (is_array($joint_order_rules)) {
                            foreach ($joint_order_rules as $rule) {
                                if (isset($rule['location']) && is_numeric($rule['location']) && isset($rule['percentage'])) {
                                    $rule_location_trigger = intval($rule['location']) - 1;
                                    if ($rule_location_trigger === $current_completion_count) {
                                        $v->balance_difference = $rule['percentage'];
                                        break; // 取第一个命中的规则
                                    }
                                }
                            }
                        }
                    }
                }
                // --- 结束"差额"计算逻辑 ---
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /*当前任务完成数量*/
    public function getTaskStatus($uid) {
        $task_set_number = Db::name('user_task')->where('user_id',$uid)->order('id DESC')->value('task_set_number');
        $task_count = Db::name('user_task')->where('task_set_number',$task_set_number)->count();
        $complete_count = Db::name('user_task')->where('task_set_number',$task_set_number)->where('user_status',1)->count();
        return $complete_count . ' / ' . $task_count;
    }

    public function getTask($uid)
    {
        $check = Db::name('user_task')->where('user_id',$uid)->count();
        if ($check == 0) {
            return '暂无任务';
        }

        $_count = Db::name('user_task')->where('user_id',$uid)->where('user_status',0)->count();

        if ($_count > 0) {
            return '未完成';
        } else {
            return '已完成';
        }

    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a", [], 'strip_tags');
            if (empty($params['username'])) {
                $this->error('账号不可为空');
            }
            if (\app\common\model\User::getByUsername($params['username'])) {
                $this->error('账号已存在');
            }
            if (empty($params['nickname'])) {
                $this->error('昵称不可为空');
            }
            if (\app\common\model\User::getByNickname($params['nickname'])) {
                $this->error('昵称已存在');
            }

            if (empty($params['mobile'])) {
                $this->error('手机号不可为空');
            }
            if (\app\common\model\User::getByMobile($params['mobile'])) {
                $this->error('手机号已存在');
            }

            if(empty($params['password'])) {
                $this->error('密码不可为空');
            }

            $salt = \fast\Random::alnum();
            $params['password_show'] = $params['password'];
            $params['password'] = \app\common\library\Auth::instance()->getEncryptPassword($params['password'], $salt);
            $params['salt'] = $salt;

            if (empty($params['pay_password'])) {
                $this->error('提现密码不可为空');
            }
            $params['invitation_code'] = $this->getCode();

            $params['createtime'] = time();
            Db::name('user')->insert($params);
            $this->success();
        }

        $this->view->assign('adminList', build_select('row[admin_id]', \app\admin\model\Admin::column('id,nickname'), 1, ['class' => 'form-control selectpicker']));
        return $this->view->fetch();
    }

    public function getCode()
    {

        $i = 1;
        while ($i <= 1000) {
            $code = Random::alnum(5);
            if (!\app\common\model\User::getByInvitationCode($code)) {
                return $code;
            }
            $i++;
        }
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a", [], 'strip_tags');
            // 密码是否变动
            if (isset($params['password'])) {
                if ($params['password']) {
                    $salt = \fast\Random::alnum();
                    $params['password_show'] = $params['password'];
                    $params['password'] = \app\common\library\Auth::instance()->getEncryptPassword($params['password'], $salt);
                    $params['salt'] = $salt;
                } else {
                    unset($params['password']);
                }
            } else {
                unset($params['password']);
            }
            // 余额是否有变动
            if (isset($params['amount']) && $params['amount'] != 0) {
                MoneyLog::create(['user_id' => $row['id'], 'money' => $params['amount'], 'before' => $row['money'], 'after' => $row['money'] + $params['amount'], 'memo' => '管理员变更金额']);
                $params['money'] = $row['money'] + $params['amount'];

                /*$agentInfo = Db::name('admin')->where('id',$row['admin_id'])->find();
                $str = '代理：'.$agentInfo['nickname']. ' 的客户：'. $row->nickname .' 下单：'.$params['amount'];
                if(strpos($row['remark'], '代打') !== false) {
                    $str = '代理：'.$agentInfo['nickname']. ' 的代打：'. $row->nickname .' 下单：'.$params['amount'];
                }
               $ret = sendTelegramMessage($str);
                if(strpos($row['remark'], '客户') !== false) {
                    $ret1 = sendTelegramMessageBattlefieldReport($str);
                }*/


            }
            unset($params['amount']);
            if (is_array($params['joint_order_setup'])) {
                $params['joint_order_setup'] = json_encode($params['joint_order_setup']);
                // 插入卡单规则变动记录
                if($row['joint_order_setup'] != $params['joint_order_setup']){
                    Db::name('user_joint_order_setup')->insert([
                        'uid'=>$row['id'],
                        'joint_order_setup' => $params['joint_order_setup'],
                        'joint_order_setup_old' => $row['joint_order_setup'],
                        'create_time' => date('Y-m-d H:i:s',time())
                    ]);
                }

            } else {
                $params['joint_order_setup'] = '';
            }
            Db::name('user')->where('id', $row['id'])->update($params);
            $this->success();
        }
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $this->view->assign('user_level',$this->getUserLevel($row['money']));
        $this->view->assign('withdraw', Db::name('withdraw')->where('user_id',$row['id'])->whereIn('status',['0','1'])->sum('amount'));
        $this->view->assign('rechargeAmount', Db::name('user_money_log')->where('user_id',$row['id'])->where('type',0)->sum('money'));
        $this->view->assign('adminList', build_select('row[admin_id]', \app\admin\model\Admin::column('id,nickname'), $row['admin_id'], ['class' => 'form-control selectpicker']));
        $this->view->assign('row', $row);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }
    public function getUserLevel($money){

        // 会员等级升级条件
        $levelList = Db::name('level')->where('status',1)->select();
        foreach ($levelList as $item) {
            if ($money  >= $item['min_money'] && $money < $item['max_money']) {
                return $item['title'];
            }
        }
    }

    public function getUserCommR($money){

        // 会员等级升级条件
        $levelList = Db::name('level')->where('status',1)->select();
        foreach ($levelList as $item) {
            if ($money  >= $item['min_money'] && $money < $item['max_money']) {
                return $item['earnings_ratio'];
            }
        }
    }


    /*重置会员任务*/
    public function reissueTask($ids=""){
        $user = $this->model->get($ids);
        // 验证账号余额
        if($user->money <= 0) {
            $this->error('账号余额不足');
        }
        // 验证账号是否还有未完成订单
        if (Db::name('user_task')->where('user_id',$ids)->where('user_status',0)->count()) {
            Db::name('user_task')->where('user_id',$ids)->where('user_status',0)->update(['user_status'=>1,'user_status_time'=>time()]);
        }
        // 任务组数
        $taskNumber = $user->order_quantity;
        if(empty($taskNumber)) {
            $this->error('请先设置会员每组订单数');
        }
        $taskIds = Db::name('task')->whereNull('deletetime')->column('id');
        if (count($taskIds) < $user->order_quantity) {
            $this->error('任务库任务不足,请添加任务');
        }
        // 可分配任务数组
        $userUaskIds = $this->reissueTaskPost($taskIds,$user->order_quantity);
        $userTask = array();
        $task_set_number = date('YmdHis').mt_rand(100,999);
        foreach ($userUaskIds as $key => $item) {
            // 获取任务规则
            $getJointOrderSetups = $this->getJointOrderSetup($user['joint_order_setup'],$key+1);
            if ($getJointOrderSetups) {
                // 任务内容
                $userTaskGroupIds = $this->reissueTaskPost($taskIds,$getJointOrderSetups['number']);
            } else {
                $userTaskGroupIds = $item;
            }
            $task = Db::name('task')->whereIn('id',$userTaskGroupIds)->select();
            $title = [];
            $image = [];
            foreach($task as $value) {
                $title[] = $value['title'];
                $image[] = $value['image'];
            }
            $userTask[] = [
                'user_id' => $ids,
                'task_set_number' => $task_set_number,
                'task_id' => $item,
                'task_name' => json_encode($title),
                'task_image' => json_encode($image),
                'task_sn' => \fast\Random::alnum(8),
                'commission' => 0,
                'ratio' => 0,
                'pirce' => 0,
                'multiple' => 1,
                'createtime' => time(),
            ];
        }
        Db::name('user_task')->insertAll($userTask);
        Db::name('user')->where('id',$ids)->update(['is_apply'=>0]);
        /*
        $agentInfo = Db::name('admin')->where('id',$user['admin_id'])->find();
        $str = '代理：'.$agentInfo['nickname'].' 的客户：'. $user->nickname .' 重置任务';
        if(strpos($user->remark, '代打') !== false) {
            $str = '代理：'.$agentInfo['nickname'].' 的代打：'. $user->nickname .' 重置任务';
        }
        sendTelegramMessage($str);*/

        $this->success('重置任务成功');
    }

    public function reissueTaskPost($taskIds,$number){
        $originalArray = $taskIds;
        $randomValues = [];
        while (count($randomValues) < $number) {
            $randomIndex = rand(0, count($originalArray) - 1);
            $randomValue = $originalArray[$randomIndex];
            if (!in_array($randomValue, $randomValues)) {
                $randomValues[] = $randomValue;
            }
        }
        return $randomValues;
    }
    public function getJointOrderSetup($jointOrderSetup,$key) {
        $jointOrderSetupArr = json_decode($jointOrderSetup, true);
        if (empty($jointOrderSetupArr)) return;
        foreach ($jointOrderSetupArr as $item) {
            if ($item['location'] == $key) {
                return $item;
            }
        }
        return;
    }

    public function remark($ids)
    {
        $row = $this->model->get($ids);
        if ($this->request->isPost()) {
            $this->token();
            $params = $this->request->post("row/a", [], 'strip_tags');
            Db::name('user')->where('id', $ids)->update(['remark'=>$params['remark']]);
            $this->success();
        }
        $this->view->assign('row', $row);
        return $this->view->fetch();
    }

    public function moneyrecharge($ids)
    {
        $row = $this->model->get($ids);
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            $money = $params['money'];
            if ($money != 0) {
                if($money < 0) {
                    if($row['money'] + $money < 0) {
                        $this->error('余额不足！');
                    }
                }

                Db::startTrans();
                try{
                    Db::name('user')->where('id',$row['id'])->setInc('money',$money);
                    Db::name('user_money_log')->insert([
                        'user_id' => $row['id'],
                        'money' => $money,
                        'before' => $row['money'],
                        'after'  => bcadd($row['money'],$money,2),
                        'memo'   => '管理员变更金额',
                        'createtime' => time()
                    ]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                }
            } else {
                $this->error('请输入数字');
            }
            $this->success('修改成功');
        }
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 联单设置 (独立弹窗)
     */
    public function jointOrderSetup($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 检查数据权限：确保当前管理员有权限操作这个用户
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");

            // --- BEGIN DEBUG ---
            error_log("[jointOrderSetup] Received POST params: " . print_r($params, true));
            if (isset($params['joint_order_setup'])) {
                error_log("[jointOrderSetup] joint_order_setup value from POST: " . $params['joint_order_setup']);
            } else {
                error_log("[jointOrderSetup] joint_order_setup value IS NOT SET in POST.");
            }
            // --- END DEBUG ---

            if (isset($params['joint_order_setup'])) {
                $new_joint_order_setup_json = $params['joint_order_setup'];
                // 校验JSON格式 (如果需要，允许空字符串以清除设置)
                if (!empty($new_joint_order_setup_json)) {
                    json_decode($new_joint_order_setup_json);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        $this->error(__('Invalid JSON format for Joint Order Setup'));
                    }
                } else {
                    $new_joint_order_setup_json = ''; // 确保空提交被视为空字符串
                }

                // 记录更改 (与 edit 方法中的逻辑类似)
                if ($row['joint_order_setup'] != $new_joint_order_setup_json) {
                    Db::name('user_joint_order_setup')->insert([
                        'uid' => $row['id'],
                        'joint_order_setup' => $new_joint_order_setup_json,
                        'joint_order_setup_old' => $row['joint_order_setup'],
                        'create_time' => date('Y-m-d H:i:s', time())
                    ]);
                }
                $update_data = ['joint_order_setup' => $new_joint_order_setup_json];
                $this->model->where('id', $row['id'])->update($update_data);
                $this->success('联单设置保存成功');
            } else {
                // 如果 joint_order_setup 字段未提交，视为清空
                if ($row['joint_order_setup'] !== null && $row['joint_order_setup'] !== '') {
                     Db::name('user_joint_order_setup')->insert([
                        'uid' => $row['id'],
                        'joint_order_setup' => '',
                        'joint_order_setup_old' => $row['joint_order_setup'],
                        'create_time' => date('Y-m-d H:i:s', time())
                    ]);
                }
                $this->model->where('id', $row['id'])->update(['joint_order_setup' => '']);
                $this->success('联单设置已清空');
            }
            return; // POST请求处理完毕
        }

        // 将数据传递给视图
        $this->view->assign("row", $row->toArray());

        return $this->view->fetch();
    }

    /**
     * 获取指定管理员及其所有下级管理员的ID列表
     * @param int $adminId 当前管理员ID
     * @return array 包含当前管理员及其所有下级管理员ID的数组
     */
    private function _getSubordinateAdminIds($adminId)
    {
        $subordinateIds = [];
        if (!$adminId) {
            return $subordinateIds;
        }

        $subordinateIds[] = (int)$adminId; // Start with the admin themselves
        $queue = [(int)$adminId]; // Queue for BFS traversal to find children

        $maxDepth = 10; // Safety break for very deep or circular hierarchies
        $currentDepth = 0;

        while (!empty($queue) && $currentDepth < $maxDepth) {
            $parentId = array_shift($queue);
            try {
                // 假设管理员表名为 admin (FastAdmin的Db::name会自动处理表前缀)
                $children = Db::name('admin')->where('pid', $parentId)->column('id');

                if (!empty($children)) {
                    foreach ($children as $childId) {
                        $childId = (int)$childId;
                        if (!in_array($childId, $subordinateIds)) {
                            $subordinateIds[] = $childId;
                            $queue[] = $childId;
                        }
                    }
                }
            } catch (\Exception $e) {
                // 记录错误日志，然后继续尝试处理队列中的其他项，或者直接返回已收集的ID
                Log::error("[UserCtrl][getSubordinateAdminIds] Error fetching children for parentId {$parentId}: " . $e->getMessage());
                // 在这里可以选择是中断循环 (break;)，还是继续 (当前会继续下一轮while)
                // 如果数据库连接或表名持续错误，这里会反复记录日志。但至少不会直接500。
            }
            $currentDepth++;
        }
        return array_unique($subordinateIds);
    }
}
