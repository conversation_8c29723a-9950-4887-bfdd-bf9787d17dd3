<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="username" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Task_sn')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-task_sn" class="form-control" name="row[task_sn]" type="text" value="{$row.task_sn|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Commission')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-commission" class="form-control" step="0.01" name="row[commission]" type="number" value="{$row.commission|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ratio')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ratio" class="form-control" step="0.01" name="row[ratio]" type="number" value="{$row.ratio|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pirce')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pirce" class="form-control" step="0.01" name="row[pirce]" type="number" value="{$row.pirce|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Multiple')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-multiple" class="form-control" name="row[multiple]" type="number" value="{$row.multiple|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_status" class="form-control" name="row[user_status]" type="number" value="{$row.user_status|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[user_status]', ['0'=>__('User_status 0'), '1'=>__('User_status 1')], $row['user_status'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_status_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_status_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[user_status_time]" type="text" value="{:$row.user_status_time?datetime($row.user_status_time):''}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
