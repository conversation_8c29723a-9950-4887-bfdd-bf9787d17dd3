<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[id]" value="{$row.id|htmlentities}">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Admin.nickname')}:</label>
        <div class="col-xs-12 col-sm-4">
            {$adminList}
        </div>
    </div>
    <div class="form-group">
        <label for="c-username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-username" data-rule="required" disabled class="form-control" name="row[username]" type="text" value="{$row.username|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-amount" class="control-label col-xs-12 col-sm-2">变动金额:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-amount" data-rule="required" class="form-control" name="row[amount]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label for="c-money" class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-money" data-rule="required" disabled class="form-control" name="" type="number" value="{$row.money|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-password" data-rule="password" class="form-control" name="row[password]" type="password" value="" placeholder="{:__('Leave password blank if dont want to change')}" autocomplete="new-password" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('用户登录密码')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-password_show" data-rule="" class="form-control" disabled name="" type="text" value="{$row.password_show|htmlentities}"/>
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Pay_password')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-pay_password" data-rule="" class="form-control" name="row[pay_password]" type="text" value="{$row.pay_password|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-mobile" data-rule="" class="form-control" name="row[mobile]" type="text" value="{$row.mobile|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Level')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input data-rule="" class="form-control" type="text" disabled value="{$user_level|htmlentities}">
        </div>
    </div>



    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Withdraw')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input data-rule="" class="form-control" type="text" disabled value="{$withdraw|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('RechargeAmount')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input data-rule="" class="form-control" type="text" disabled value="{$rechargeAmount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-money" class="control-label col-xs-12 col-sm-2">{:__('Credit_score')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-credit_score" data-rule="required" class="form-control" name="row[credit_score]" type="number" value="{$row.credit_score|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-address" data-rule="" class="form-control" name="row[address]" type="text" value="{$row.address|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Address_type')}:</label>
        <div class="col-xs-12 col-sm-4">
            {:build_select('row[address_type]',[''=>'请选择钱包区块链','ERC20'=>__('ERC20'), 'TRC20'=>__('TRC20')],$row['address_type'])}
        </div>
    </div>

    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Number_of_tasks_completed')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-number_of_tasks_completed" data-rule="required" disabled class="form-control" name="row[number_of_tasks_completed]" type="text" value="{$row.number_of_tasks_completed|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Task_number')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-task_number" data-rule="required" disabled class="form-control" name="row[task_number]" type="text" value="{$row.task_number|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Order_quantity')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-order_quantity" data-rule="required" class="form-control" name="row[order_quantity]" type="text" value="{$row.order_quantity|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Joint_order_setup')}:</label>
        <div class="col-xs-12 col-sm-4" id="input_box">
            <table class="table table-responsive fieldlist" data-name="row[joint_order_setup]" data-template="testtpl" data-tag="tr">
                <tr>
                    <td>任务位置</td>
                    <td>联单数</td>
                    <td>佣金倍数</td>
                    <td>增长金额</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="5"><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a></td>
                </tr>
                <textarea name="row[joint_order_setup]" class="form-control hide" cols="30" rows="5">{$row.joint_order_setup}</textarea>
            </table>
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-commission" class="control-label col-xs-12 col-sm-2">{:__('Commission')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-commission" data-rule="required" class="form-control" name="row[commission]" type="number" value="{$row.commission|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-task_commission" class="control-label col-xs-12 col-sm-2">任务佣金比例（%）:</label>
        <div class="col-xs-12 col-sm-4">
            <input id="c-task_commission" data-rule="required" class="form-control" name="row[task_commission]" type="number" value="{$row.task_commission|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
<script type="text/html" id="testtpl">
    <tr class="form-inline">
        <td><input type="text" name="<%=name%>[<%=index%>][location]" class="form-control" value="<%=row['location']%>" size="5"></td>
        <td><input type="text" name="<%=name%>[<%=index%>][number]" class="form-control" value="<%=row['number']%>" size="5"></td>
        <td><input type="text" name="<%=name%>[<%=index%>][multiple]" class="form-control" value="<%=row['multiple']%>" size="5"></td>
        <td><input type="text" name="<%=name%>[<%=index%>][percentage]" class="form-control" value="<%=row['percentage']%>" size="5"></td>
        <td><span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span></td>
    </tr>
</script>
