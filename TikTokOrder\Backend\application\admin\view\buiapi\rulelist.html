<div class="tab-pane fade  active in" id="customer">
    {:build_heading()}
	<div class="fixed-table-toolbar">
		<div class="bs-bars pull-left">
			<div id="toolbar" class="toolbar">
				{:build_toolbar('refresh')}
				<a href="javascript:;" id="rule_add" data-url="buiapi/ruleadd?table={$table}" data-table-id="table" data-title="添加规则"
					class="btn btn-success btn-add rule_add" title="添加规则"><i class="fa fa-plus"></i> 添加规则</a>
				<a href="javascript:;" data-title="显示字段" data-url="buiapi/fieldview?table={$table}"  class="btn btn-primary btn-add field_view" style="background-color: #0047b3;" title="显示字段"><i class="fa fa-street-view"></i> 显示字段</a>

			</div>
		</div>
	</div>
	
	<div class="widget-body no-padding">
		<table class="table table-striped">
			<thead>
				<tr>
					<th width="15%">字段标题</th>
					<th width="45%">字段展示</th>
					<th width="35%">标签</th>
					<th width="1%"></th>
				</tr>
			</thead>
			
			<tbody>
				{foreach $list as $index=>$vo}
				<tr>
					<td class="input-group input-group-sm">{$vo.title}（{$vo.field}）</td>
					<td>
						<div class="row">
							{if condition="in_array($vo['type'],['enum','set'])"}
							<div class="input-group input-group-sm col-sm-8 col-xs-12"
								title='{if $vo.type="$vo[type] eq enum"}单选{else/}多选{/if}'>
								<select id="c-flag" name="{$vo.field}" class="form-control">
									{foreach $vo.slist as $key=>$val}
										<option value="{$key}">{$val}</option>
									{/foreach}
								</select>
								<span class="input-group-btn input-group-sm"><a href="javascript:;"
										class="btn btn-default btn-sm">{if $vo.type="$vo[type] eq
										enum"}单选{else/}多选{/if}</a></span>
							</div>
							{else /}
							<div class="input-group input-group-sm col-sm-8 col-xs-12">
								<input type="text" name="{$vo.field}" value="" class="form-control" title="{$vo.type}">
								<span class="input-group-btn input-group-sm"><a href="javascript:;"
										class="btn btn-default btn-sm">{$vo['type']}</a></span>
							</div>
							{/if}
						</div>

					</td>
					<td>
						{foreach $vo.tags as $index=>$vo}
						<a href="javascript:;" data-id="{$vo.id}" data-type="{$vo.type}" data-rule="{$vo.rule}"
							class="delete" title="点击删除"><span style="background-color:{$vo.color}"
								class="label label-info">{$vo.tag}<font size="2"> ×</font></span></a>
						{/foreach}

					</td>
					<td>
						<a href="javascript:;" data-id="{$vo.id}" class="hiddens" style="display:normal" title="点击隐藏">
							<font size="4"> × </font>
						</a>
					</td>
				</tr>
				{/foreach}
			</tbody>
		</table>
	</div>
</div>