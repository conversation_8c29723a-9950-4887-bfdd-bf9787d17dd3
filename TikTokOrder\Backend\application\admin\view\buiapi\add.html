
<form id="add-form" style="weigth:100px;" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">{:__('数据库')}:</label>
		<div class="col-xs-12 col-sm-8">
			<select id="c-flag" data-rule="required" class="form-control selectpicker" data-live-search="true" multiple="" name="row[tables][]">
				{volist name="tableList" id="item"}
					<option value="{$key}|{$item}">{$item}({$key})</option>
				{/volist}
			</select>
		</div>
	</div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
