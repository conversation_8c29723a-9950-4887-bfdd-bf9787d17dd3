/*******************************************************************************
 * KindEditor - WYSIWYG HTML Editor for Internet
 * Copyright (C) 2006-2011 kindsoft.net
 *
 * <AUTHOR> <<EMAIL>>
 * @site http://www.kindsoft.net/
 * @licence http://www.kindsoft.net/license.php
 *******************************************************************************/

KindEditor.plugin('template', function (K) {
    var self = this, name = 'template', lang = self.lang(name + '.'),
        htmlPath = self.pluginsPath + name + '/html/';

    function getFilePath(fileName) {
        return htmlPath + fileName + '?ver=' + encodeURIComponent(K.DEBUG ? K.TIME : K.VERSION);
    }

    self.clickToolbar(name, function () {
        var lang = self.lang(name + '.'),
            arr = ['<div class="ke-dialog-content-inner" style="padding-top:0">',
                '<div class="ke-dialog-row ke-clearfix">',
                '<div class="ke-header" style="height: 32px;">',
                // left start
                lang.selectTemplate + ' <select class="ke-select">'];
        K.each(lang.fileList, function (key, val) {
            arr.push('<option value="' + key + '">' + val + '</option>');
        });
        html = [arr.join(''),
            '</select>',
            // right start
            '<input type="checkbox" id="keReplaceFlag" class="checkbox" name="replaceFlag" value="1" /> <label for="keReplaceFlag">' + lang.replaceContent + '</label>',
            '</div>',
            '</div>',

            //template iframe
            '<iframe class="ke-textarea" frameborder="0" style="width:458px;height:260px;background-color:#FFF;"></iframe>',
            '</div>'].join('');
        var dialog = self.createDialog({
            name: name,
            width: Math.min(document.body.clientWidth, 500),
            title: self.lang(name),
            body: html,
            yesBtn: {
                name: self.lang('yes'),
                click: function (e) {
                    var doc = K.iframeDoc(iframe);
                    self[checkbox[0].checked ? 'html' : 'insertHtml'](doc.body.innerHTML).hideDialog().focus();
                }
            }
        });
        var selectBox = K('select', dialog.div),
            checkbox = K('[name="replaceFlag"]', dialog.div),
            iframe = K('iframe', dialog.div);
        checkbox[0].checked = true;
        iframe.attr('src', getFilePath(selectBox.val()));
        selectBox.change(function () {
            iframe.attr('src', getFilePath(this.value));
        });
    });
});
