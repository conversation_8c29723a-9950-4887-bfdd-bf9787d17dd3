<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;

/**
 * 任务申请列管理
 *
 * @icon fa fa-circle-o
 */
class Apply extends Backend
{

    /**
     * Apply模型对象
     * @var \app\admin\model\Apply
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Apply;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user','taskset'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','createtime','status','statustime']);
                $row->visible(['user']);
				$row->getRelation('user')->visible(['username']);
                $row->visible(['taskset']);
                $row->getRelation('taskset')->visible(['title']);
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
    
    public function edit($ids = null){
        $row = $this->model->get($ids);
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a", [], 'strip_tags');
            
            if ($row['status'] != 0) {
                $this->error('当前任务申请已审核，无需重复审核');
            }
            if (empty($params['task_set_id'])) {
                $this->error('请分配任务组');
            }
            
            if ($params['status'] == 1) {
                $taskList = Db::name('task_set')->where('id', $params['task_set_id'])->find();
                $taskIds = explode(',', $taskList['task_ids']);
                $data = [];
                // 确认当前会员等级
                $ratio = $this->getUserRatio($row['user_id']);//0.05;
                foreach ($taskIds as $item) {
                    $taskInfo = Db::name('task')->where('id',$item)->whereNull('deletetime')->find();
                    if ($taskInfo) {
                        if ($taskInfo['commission_rate'] > 0) {
                            $ratios = $taskInfo['commission_rate'];
                        } else {
                            $ratios = $ratio;
                        }
                        
                        // 佣金
                        $commission = $taskInfo['price'] * $ratio * $taskInfo['commission_multiple'];
                        $commission = bcmul($commission,0.01,2);
                        if($ratio > 0) {
                            $data[] = [
                                'user_id' => $row['user_id'],
                                'user_apply_task_id' => $ids,
                                'task_id' => $taskInfo['id'],
                                'type' => $taskInfo['type'],
                                'task_name' => $taskInfo['title'],
                                'task_image' => $taskInfo['image'],
                                'group_task_name' => $taskInfo['combination_title_2'],
                                'group_task_image' => $taskInfo['combination_image'],
                                'commission' => $commission,
                                'task_sn' => 'TK'.date('mdHYmd').mt_rand(1000,9999),
                                'ratio' => $ratios,
                                'pirce' => $taskInfo['price'],
                                'multiple' => $taskInfo['commission_multiple'],
                                'createtime' => time(),
                            ];
                        }
                    }
                }
                
                // 启动事务
                Db::startTrans();
                try{
                    Db::name('user_task')->insertAll($data);
                    Db::name('user_apply_task')->where('id',$row['id'])->update(['task_set_id'=>$params['task_set_id'],'status'=>'1','statustime'=>time()]);
                    // 提交事务
                    Db::commit();
                } catch (\Exception $e) {
                    // 回滚事务
                    Db::rollback();
                    $this->error('申请任务审核失败：'.$e->getMessage());
                }
            } elseif ($row['status'] == -1) {
                Db::name('user_apply_task')->where('id',$row['id'])->update(['status'=>'-1','statustime'=>time()]);
            }
            
            $this->success();
        }
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $this->assign("row", $row);
//        return parent::edit($ids);
        return $this->view->fetch();
    }
    
    /*获取会员分佣比例*/
    public function getUserRatio($uid){
        $userInfo = Db::name('user')->where('id',$uid)->find();
        // 会员等级升级条件
        $levelList = Db::name('level')->where('status',1)->select();
        foreach ($levelList as $item) {
            if ($userInfo['money']  >= $item['min_money'] && $userInfo['money'] < $item['max_money']) {
                return $item['earnings_ratio'];
            }
        }
    }
    
}
