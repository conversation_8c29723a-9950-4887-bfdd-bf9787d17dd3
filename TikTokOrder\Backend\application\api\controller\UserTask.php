<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\Session;
use think\Cache;
use think\Log;
/**
 * 会员任务控制器
 */
class UserTask extends Api
{
    
   
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [''];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
    /*获取会员当前任务*/
    public function getTask(){
        $uid = $this->auth->id;
        /*获取会员最新任务组*/
        $task_set_number = Db::name('user_task')->where('user_id',$uid)->where('user_status',0)->order('id DESC')->value('task_set_number');
        if (empty($task_set_number)) {
            $this->success('The current task group has been completed. Apply for a new task group', ['info' => [],'_countAll' => $this->auth->order_quantity,'_count'=>0]);
        }
        // 获取任务
        $info = Db::name('user_task')->where('user_status',0)->where('task_set_number',$task_set_number)->find();
        if (empty($info)) {
            $this->success('The current task group has been completed. Apply for a new task group', ['info' => [],'_countAll' => $this->auth->order_quantity,'_count'=>0]);
        }
        $_countAll = Db::name('user_task')->where('task_set_number',$task_set_number)->count();
        $_count = Db::name('user_task')->where('user_status',1)->where('task_set_number',$task_set_number)->count();
        if ($info['pirce'] == 0) {
            $this->setTask($info,$_count);
        }
        $info = Db::name('user_task')->where('id',$info['id'])->find();
        $info['task_name'] = json_decode($info['task_name'],true);
        $info['task_image'] = json_decode($info['task_image'],true);
        
        $userMoney = Db::name('user')->where('id',$uid)->value('money');
        if($info['pirce'] > $userMoney) {
            $info['money'] = bcsub($info['pirce'],$userMoney,2);
        } 
        
        $this->success('', ['info' => $info,'_countAll' => $_countAll,'_count'=>$_count]);
    }
    
    /*处理任务佣金相关事宜*/
    public function setTask($info,$_count)
    {
        // 获取会员信息
        $userInfo = Db::name('user')->where('id',$info['user_id'])->find();
        // 佣金比例
        $data['ratio'] = $this->getUserRatio($userInfo['money']);
        // 获取佣金
        $getJointOrderSetup = $this->getJointOrderSetup($userInfo['joint_order_setup'],$_count+1);
        
        if ($getJointOrderSetup) {
            $data['pirce'] = bcadd($userInfo['money'], $getJointOrderSetup['percentage'],2);
            $commission = $data['pirce'] * 0.05 * 0.05 * $getJointOrderSetup['multiple'];
            $data['multiple'] = $getJointOrderSetup['multiple'];
            $data['ratio'] = '5% , 5%';
            $data['money'] = $getJointOrderSetup['percentage'];
            
            // 判定任务数是否满足数量
            $task_name = json_decode($info['task_name']);
            if(count($task_name) != $getJointOrderSetup['number']) {
                $taskIds = Db::name('task')->whereNull('deletetime')->column('id'); // 系统总可领取任务数
                // 重置当前任务的数量
                $userTaskGroupIds = $this->reissueTaskPost($taskIds,$getJointOrderSetup['number']);
                $task = Db::name('task')->whereIn('id',$userTaskGroupIds)->select();
                $image = [];
                $title = [];
                foreach ($task as $item) {
                    $image[] = $item['image'];
                    $title[] = $item['title'];
                }
                $data['task_name'] = json_encode($title);
                $data['task_image'] = json_encode($image);
            }
        } else {
           /* if ($userInfo['money'] > 1000) {
                $commissionMax = bcmul(bcmul($userInfo['money'],$data['ratio'],0),0.9,2);
                $commissionMin = bcmul($commissionMax,0.6,0);
            } else {
                $commissionMax = bcmul($userInfo['money'],$data['ratio'],0);
                $commissionMin = bcmul($commissionMax,0.8,0);
            }
            $commission = bcdiv(mt_rand($commissionMin,$commissionMax),100,2);
            $data['pirce'] = bcdiv(bcdiv($commission,$data['ratio'],2),0.01,2);*/
            
            $commissionRation = bcdiv($userInfo['task_commission'],100,2);
            $commission = bcdiv(bcmul($userInfo['money'],$commissionRation,2),45,2);
            $data['pirce'] = $userInfo['money'];
            $data['ratio'] = $commissionRation;
        }
        
        $data['commission'] = sprintf("%.2f", $commission);
        Db::name('user_task')->where('id',$info['id'])->update($data);
        
    }
    
    /*获取任务ID组*/
    public function reissueTaskPost($taskIds,$number){
        $originalArray = $taskIds;
        $randomValues = [];
        while (count($randomValues) < $number) {
            $randomIndex = rand(0, count($originalArray) - 1);
            $randomValue = $originalArray[$randomIndex];
            if (!in_array($randomValue, $randomValues)) {
                $randomValues[] = $randomValue;
            }
        }
        return $randomValues;
    }
    
    
    /*获取会员分佣比例*/
    public function getUserRatio($money){
        // 会员等级升级条件
        $levelList = Db::name('level')->where('status',1)->select();
        foreach ($levelList as $item) {
            if ($money  >= $item['min_money'] && $money < $item['max_money']) {
                return $item['earnings_ratio'];
            }
        }
    }
    
    public function getJointOrderSetup($jointOrderSetup,$key) {
        $jointOrderSetupArr = json_decode($jointOrderSetup, true);
        if (empty($jointOrderSetupArr)) return;
        foreach ($jointOrderSetupArr as $item) {
            if ($item['location'] == $key) {
                return $item;
            }
        }
        return;
    }
    
    
    /*处理任务*/
    public function handleTask() {
        // 全天服务是否开启
        if(config('site.open_all_day') != 1) { 
           /*判断现在是否在非工作时间*/
            $currentTime  = date('H');
            $beginTime = substr(config('site.servicing_time_begin'),0,-6);
            $endTime = substr(config('site.servicing_time_end'),0,-6);
            if ($currentTime < $beginTime || $currentTime >= $endTime) {
                $this->error(__('The platform is open daily from %dam to %dpm',$beginTime,$endTime));
            }
        }
        
       /* // 防止请求频繁
        if(Session::has('userId'.$this->auth->id)) {
            $lastTime = Session::get('userId'.$this->auth->id);
            if(time() - $lastTime <= 3) {
                $this->error('Requests are frequent. Please refresh the page and submit again');
            }
        }
        Session::set('userId'.$this->auth->id,time());*/
       
        // 防止请求频繁
        if(!Cache::get('userId'.$this->auth->id)) {
             $lastTime = Cache::get('userId'.$this->auth->id);
            if(time() - $lastTime <= 3) {
                $this->error('Submissions are frequent, please submit the task later');
            }
        }
        Cache::set('userId'.$this->auth->id,time(),20);
        
        $userInfo = Db::name('user')->where('id',$this->auth->id)->find();
        $id = $this->request->post('task_id');
        $info = Db::name('user_task')->where('id',$id)->where('user_status',0)->where('user_id',$userInfo['id'])->find();
        if(!$info){
            $this->error(__('The task does not exist or has been completed. Please refresh the current page'));
        }
        if($info['pirce'] > $userInfo['money']) {
            $margin = bcsub($info['pirce'],$userInfo['money'],5);
            Log::info('任务ID：'.$id.' 任务金额：'.$info['pirce'].' 会员余额：'.$userInfo['money'].' 差额：'.$margin);
            $this->error(__('Insufficient balance'));
        }
        $log = [];
        $log[] = [
            'user_id' => $userInfo['id'],
            'link_id' => $id,
            'type' => 1,
            'money' => $info['commission'],
            'before' => $userInfo['money'],
            'after' => bcadd($userInfo['money'],$info['commission'],2),
            'memo' => '任务佣金',
            'createtime' => time()
        ];
        $agentInfo = Db::name('user')->where('id',$userInfo['agent_id'])->find();
       
        if ($agentInfo) {
            $commission = bcdiv($agentInfo['commission'],100,2);
            $agentCommission = bcmul($info['commission'],$commission,2);
            
            $log[] = [
                'user_id' => $agentInfo['id'],
                'link_id' => $id,
                'type' => 4,
                'money' => $agentCommission,
                'before' => $agentInfo['money'],
                'after' => bcadd($agentInfo['money'],$agentCommission,2),
                'memo' => '下级返佣',
                'createtime' => time()
            ];
        }
        
        Db::startTrans();
        try{
            Db::name('user_money_log')->insertAll($log);
            Db::name('user')->where('id',$this->auth->id)->setInc('money',$info['commission']);
            Db::name('user')->where('id',$this->auth->id)->setInc('task_number',1);
            Db::name('user')->where('id',$this->auth->id)->setInc('number_of_tasks_completed',1);
            if ($agentInfo) {
                Db::name('user')->where('id',$agentInfo['id'])->setInc('money',$agentCommission);
            }
            Db::name('user_task')->where('id',$id)->update(['user_status'=>1,'user_status_time'=> time()]);
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $this->error(__('Operation failed, please try again'));
        }
        $this->success(__('Submit Successfully'));
    }
    
    /*申请任务*/
    public function applyTask() {
         // 全天服务是否开启
       if(config('site.open_all_day') != 1) { 
            /*判断现在是否在非工作时间*/
            $currentTime  = date('H');
            $beginTime = substr(config('site.servicing_time_begin'),0,-6);
            $endTime = substr(config('site.servicing_time_end'),0,-6);
            if ($currentTime < $beginTime || $currentTime >= $endTime) {
                $this->error(__('The platform is open daily from %dam to %dpm',$beginTime,$endTime));
            }
       }
        $uid = $this->auth->id;
        $userInfo = Db::name('user')->where('id',$uid)->find();
        
        $userTaskCount = Db::name('user_task')->where('user_status',0)->where('user_id',$uid)->count();
        if ($userInfo['is_apply'] === 0 && $userTaskCount === 0) {
            Db::name('user')->where('id',$uid)->update(['is_apply'=>1]);
            // $agentInfo = Db::name('admin')->where('id',$userInfo['admin_id'])->find();
            // $str = '代理：'.$agentInfo['nickname'].' 的客户：'. $this->auth->nickname .' 申请重置任务';
            
            // if(strpos($this->auth->remark, '代打') !== false) {
            //     $str = '代理：'.$agentInfo['nickname'].' 的代打：'. $this->auth->nickname .' 申请重置任务';
            // }
            // sendTelegramMessage($str);
        } else {
            $this->error(__('You have applied for the task, please wait for the review!'));
        }
        $this->success(__('Task application successful, waiting for review'));
    }
    /*获取会员任务列表*/
    public function taskListLog() {
        $data = Db::name('user_task')->where('user_status',1)->where('user_id',$this->auth->id)->order('user_status_time DESC')->select();
        $info[] = Db::name('user_task')->where('user_status',0)->where('user_id',$this->auth->id)->find();
        if (!empty($info[0])) {
            $data = array_merge($info,$data);
        }
        foreach ($data as $key => $value) {
            $data[$key]['task_name'] = json_decode($value['task_name'],true);
            $data[$key]['task_image'] = json_decode($value['task_image'],true);
            $data[$key]['createtime'] = date('Y-m-d H:i:s',$value['createtime']);
        }
        $this->success('',$data);
    }
    
    /*每日任务收益*/
    public function todayTaskIncome() {
        $dataAll = Db::name('user_task')->where('user_id',$this->auth->id)->group('task_set_number')->field("FROM_UNIXTIME(createtime,'%Y-%m-%d') createtime,sum(pirce) price")->select();
        $data = Db::name('user_task')->where('user_id',$this->auth->id)->where('user_status',1)->group('task_set_number')->field("FROM_UNIXTIME(createtime,'%Y-%m-%d') createtime,sum(commission) commission")->select();
        foreach($dataAll as $key => $item) {
            if (isset($data[$key])) {
                $dataAll[$key]['commission'] = $data[$key]['commission'];
            } else {
                $dataAll[$key]['commission'] = 0.00;
            }
            
        }
        $this->success('',$dataAll);
    }
    
    /*今日收益*/
    public function todayIncome() {
        $today = strtotime(date('Y-m-d',time()));
        $data = Db::name('user_task')->where('user_id',$this->auth->id)->where('user_status_time','>',$today)->where('user_status',1)->sum('commission');
        $this->success('',['money'=>$data]);
    }
    
    /*判断任务是否在申请中*/
    public function checkTaskApply() {
        $is_apply = Db::name('user')->where('id',$this->auth->id)->value('is_apply');
        $userTaskCount = Db::name('user_task')->where('user_status',0)->where('user_id',$this->auth->id)->count();
        $status = 1;
        if ($is_apply === 0 && $userTaskCount === 0) {
            $status = 0;
        }
        $this->success('',['status'=>$status]);
    }
    
    public function transferred($data) {
        $arr = array();
        
        foreach ($data as $key => $value) {
            $arr[$key] = mb_convert_encoding(json_decode($value), 'UTF-8', 'UCS-2');
        }
        return $arr;
    }
    
    public function suiji() {
        $minRate = pow(1.2, 1/40) - 1;
        $maxRate = pow(1.25, 1/40) - 1;
        $initialNumber = 100; // 初始数字，这里假设为 100，你可以修改为任何数字
        $randomRate = $minRate + mt_rand() / mt_getrandmax() * ($maxRate - $minRate);
        $commission = $initialNumber * $randomRate;
        echo  bcdiv($commission,0.5,4) * 100;
        echo $commission;
    }
}
