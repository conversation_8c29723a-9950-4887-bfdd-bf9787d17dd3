-- 为联单设置功能添加权限规则
-- 需要在FastAdmin后台执行或通过数据库管理工具执行

-- 查找user/user的父级权限ID
SET @parent_id = (SELECT id FROM fa_auth_rule WHERE name = 'user/user' AND ismenu = 1 LIMIT 1);

-- 如果没有找到父级，先创建user/user菜单权限
INSERT IGNORE INTO fa_auth_rule (type, pid, name, title, icon, url, condition, remark, ismenu, menutype, extend, py, pinyin, createtime, updatetime, weigh, status) 
VALUES ('file', 0, 'user/user', '会员管理', 'fa fa-user', '', '', '', 1, NULL, '', 'hygl', 'huiyuanguanli', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');

-- 重新获取父级ID
SET @parent_id = (SELECT id FROM fa_auth_rule WHERE name = 'user/user' AND ismenu = 1 LIMIT 1);

-- 添加联单设置权限规则
INSERT IGNORE INTO fa_auth_rule (type, pid, name, title, icon, url, condition, remark, ismenu, menutype, extend, py, pinyin, createtime, updatetime, weigh, status) 
VALUES ('file', @parent_id, 'user/user/jointordersetup', '联单设置', '', '', '', '会员联单设置权限', 0, NULL, '', 'ldsz', 'liandanshezhi', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 'normal');

-- 查看添加的权限规则
SELECT id, pid, name, title, ismenu, status FROM fa_auth_rule WHERE name LIKE '%user%' ORDER BY pid, id;
