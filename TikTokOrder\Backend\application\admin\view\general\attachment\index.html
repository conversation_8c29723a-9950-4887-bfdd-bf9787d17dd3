<div class="panel panel-default panel-intro">

    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="category">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="categoryList" item="vo"}
            <li><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
            <li class="pull-right dropdown filter-type">
                <a href="javascript:" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-filter"></i> {:__('Filter Type')}</a>
                <ul class="dropdown-menu text-left" role="menu">
                    <li class="active"><a href="javascript:" data-value="">{:__('All')}</a></li>
                    {foreach name="mimetypeList" id="item"}
                    <li><a href="javascript:" data-value="{$key}">{$item}</a></li>
                    {/foreach}
                </ul>
            </li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add,edit,del')}
                        <a class="btn btn-info btn-classify dropdown-toggle btn-disabled disabled {:$auth->check('general/attachment/edit')?'':'hide'}"><i class="fa fa-arrow-right"></i> {:__('Classify')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('general/attachment/edit')}"
                           data-operate-del="{:$auth->check('general/attachment/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<script id="typetpl" type="text/html">
    <div class="row">
        <div class="col-xs-12">
            <select name="category" class="form-control">
                <option value="">{:__('Please select category')}</option>
                {foreach name="categoryList" id="item"}
                <option value="{$key}">{$item}</option>
                {/foreach}
            </select>
        </div>
    </div>
</script>
